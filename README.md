# Smart Alert Reminder Service

A microservice responsible for sending reminder notifications for unacknowledged alert incidents.

## Overview

The Smart Alert Reminder Service periodically checks for active alert incidents that have not been acknowledged or resolved and sends reminder notifications to the subscribed users through configured channels (email, SMS, WhatsApp).

This service is part of the Smart Alert ecosystem that includes:
- Smart Alert Brain Service (main orchestrator)
- Smart Alert Notification Service (sending notifications through different channels)
- Smart Alert Email Notification Service (email-specific notifications)
- Smart Alert Reminder Service (this service)
- Escalation Trigger Microservice (handles escalations)

## Features

- Scheduled checking for unacknowledged alerts that need reminders
- Distributed lock mechanism to prevent duplicate reminders
- Support for multiple notification channels (email, SMS, WhatsApp)
- Configurable reminder intervals
- Integration with Kafka for message publishing

## Architecture

The service follows a modular architecture:

- **Jobs**: Scheduled tasks that run periodically
- **Services**: Business logic for processing and sending reminders
- **Repositories**: Data access layer for database operations
- **Connections**: Infrastructure connections (Kafka, Redis, PostgreSQL)
- **Config**: Configuration settings
- **Utils**: Utility functions and helpers

## Prerequisites

- Node.js 18+
- PostgreSQL
- Redis
- Kafka

## Configuration

Copy the sample environment file and adjust the values:

```bash
cp sample.env .env
```

Key configurations:

- `REMINDER_INTERVAL_MINUTES`: How often to send reminders (default: 30 minutes)
- `REMINDER_MAX_COUNT`: Maximum number of reminders to send (default: 3)
- `REMINDER_BATCH_SIZE`: Number of alerts to process in each run (default: 100)

## Installation

```bash
# Install dependencies
npm install

# Start the service
npm start

# For development with auto-restart
npm run dev
```

## Docker

Build and run with Docker:

```bash
# Build the image
docker build -t smart-alert-reminder-service .

# Run the container
docker run -p 3000:3000 --env-file .env smart-alert-reminder-service
```

## Data Flow

1. The scheduled job runs every 5 minutes
2. The service queries the database for unacknowledged alerts
3. For each alert, it retrieves subscribers and notification templates
4. If eligible, the service sends reminder notifications via Kafka
5. The Smart Alert Notification Service processes the Kafka messages and delivers notifications

## Database Schema

The service interacts with the following database tables:

- `alert_incident_history`: Track alert incidents and their status
- `alert_inventory`: Alert definitions and configurations
- `alert_subscribers`: Users subscribed to alerts
- `notification_message_template`: Templates for different notification types
- `alert_template`: Template definitions for alerts

## Development

```bash
# Format code
npm run format

# Lint code
npm run lint
```

## License

ISC
