MessagePayloadBuilder.build()
         │
         ▼
+---------------------------------------------+
| Initialize Channel Details                  |
| (Set default metadata,                      |
|  prepare email/sms/whatsapp)                |
+---------------------------------------------+
         │
         ▼
+---------------------------------------------+
| Check for Recipe Alert with Block ID        |
| (For recipe alerts, get incident block_id   |
|  and fetch block-specific description)      |
+---------------------------------------------+
         │
         ▼
+---------------------------------------------+
| Fetch Alert Subscribers                     |
| (from Postgres: alert_                      |
| subscribers with flags)                     |
+---------------------------------------------+
         │
         ▼
+------------------------------------------+
| Process Each Subscriber                  |
|  └─ addSubscriberToChannel()             |
|     (Add user to channel recipient list) |
+------------------------------------------+
         │
         ▼
+---------------------------------------------+
| Fetch Templates / Asset / Site              |
| (Parallel: fetchNotificationTemplates,      |
|  fetchAssetDetails, fetchSiteName)          |
|  └─ For recipe alerts with blockId:         |
|     use block-specific description          |
|     from children_recipes table             |
+---------------------------------------------+
         │
         ▼
+--------------------------------------------+
| Populate Channel Details                   |
|  └─ Attach channel-specific content and    |
|     metadata from templates                |
+--------------------------------------------+
         │
         ▼
+--------------------------------------------+
| Global Notification Settings Filter        |
| (filterSubscribersByGlobalPreferences())   |
+--------------------------------------------+
         │
         ▼
+---------------------------------------------+
| Collect Unique userIds from all channels    |
+---------------------------------------------+
         │
         ▼
+---------------------------------------------+
| Batch Lookup in Redis using MGET            |
|   ├─ For cached users: use Redis value      |
|   └─ For missing users: fetch from DynamoDB, |
|       then cache with TTL (5 minutes)        |
+---------------------------------------------+
         │
         ▼
+---------------------------------------------+
| For Each Channel:                           |
|  └─ Remove recipients with global setting   |
|     { channel: 0 } (i.e. JouleRecipe '0')   |
+---------------------------------------------+
         │
         ▼
+---------------------------------------------+
| Return Final Channel Details                |
| (Only recipients with enabled global prefs) |
+---------------------------------------------+
