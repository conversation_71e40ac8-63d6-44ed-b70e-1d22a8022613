/**
 * OpenTelemetry tracing setup with SigNoz.
 *
 * 🔹 Purpose:
 * Initializes OpenTelemetry SDK to collect and export traces to SigNoz.
 *
 * 🔹 Key Components:
 * - NodeSDK: Core tracing orchestrator.
 * - OTLPTraceExporter: Sends traces to SigNoz via HTTP using access token.
 * - getNodeAutoInstrumentations: Auto-instruments modules like HTTP, Express.
 * - Resource: Sets attributes like `service.name` for SigNoz.
 * - sentry.service: Logs errors if SDK init fails.
 *
 * 🔹 Required Env Vars:
 * - SIGNOZ_SERVICE_NAME
 * - SIGNOZ_OTLP_ENDPOINT
 * - SIGNOZ_INGESTION_KEY
 *
 * 🔹 Flow:
 * 1. Validates env vars.
 * 2. Logs errors to Sen<PERSON> if setup fails.
 * 3. Sets up exporter + SDK with auto-instrumentation.
 * 4. Starts SDK to begin sending trace data.
 */


const process = require('process');
const { NodeSDK } = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-trace-otlp-http');
const { defaultResource: Resource } = require('@opentelemetry/resources');
const { ATTR_SERVICE_NAME } = require('@opentelemetry/semantic-conventions');
const os = require('os');
const Sentry = require('./api/services/logTransport/sentry.service')

// Constants
const SERVICE_NAME =  process.env.SIGNOZ_SERVICE_NAME || 'jt-api-v2';
const OTLP_URL = process.env.SIGNOZ_OTLP_ENDPOINT ;
const ACCESS_TOKEN = process.env.SIGNOZ_INGESTION_KEY;


(async () => {
  try {
    if (!OTLP_URL || !ACCESS_TOKEN || !SERVICE_NAME) {
      sails.log.error('SIGNOZ_OTLP_ENDPOINT, SIGNOZ_INGESTION_KEY, or SERVICE_NAME environment variable is missing.');
      Sentry.setTag('hostname', os.hostname());
      Sentry.setContext('Signoz Connectivity', {
          errorMessage: 'Missing environment variables.',
      });
     Sentry.captureException(
        Object.assign(
          new Error('Missing SIGNOZ_OTLP_ENDPOINT, SIGNOZ_INGESTION_KEY, or SERVICE_NAME environment variables'), 
          { name: 'MissingSignozEnvironmentVariables' }
        )
      );
      return;
    }

    const traceExporter = new OTLPTraceExporter({
      url: OTLP_URL,
      headers: {
        'signoz-access-token': ACCESS_TOKEN,
      },
    });

    const sdk = new NodeSDK({
      traceExporter,
      instrumentations: [getNodeAutoInstrumentations()],
      resource: new Resource({
        [ATTR_SERVICE_NAME]: SERVICE_NAME,
      }),
    });
    sdk._serviceName = SERVICE_NAME;
    await sdk.start();
    sails.log(`OpenTelemetry SDK started for service: ${SERVICE_NAME} with host: ${os.hostname()}`);

  } catch (err) {
    sails.log.error('Error starting OpenTelemetry SDK:', err);
    Sentry.setTag('hostname', os.hostname());
    Sentry.setContext('Signoz Connectivity', {
      errorMessage: 'Signoz connection failure',
    });
    Sentry.captureException(Object.assign(err, { name: 'SignozConnectionError' }));
  }
})();
