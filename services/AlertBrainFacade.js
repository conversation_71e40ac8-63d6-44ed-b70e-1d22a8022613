import AlertService from './Alert.service.js';
import logger from '../utils/logger.js';
import moment from 'moment-timezone';
import EventTransformer from './EventTransformer.js';

export class AlertBrainFacade {
  constructor(alertEventPacket) {
    this.alertEventPacket = alertEventPacket;
  }

  static async build(event) {
    const eventTransformerInstance = new EventTransformer();
    const alertEventPacket = await eventTransformerInstance.transform(event);
    if (!alertEventPacket) return null;
    return new AlertBrainFacade(alertEventPacket);
  }

  isStateFullEvent() {
    return this.alertEventPacket.STATEFUL_ALERT_FLAG === 1;
  }

  async processStateFullEvent() {
    const {
      siteId,
      eventName,
      eventTimestamp,
      alertInventoryId,
      transactionId,
      eventOffsetId,
      blockId
    } = this.alertEventPacket;

    const logContext = {
      siteId,
      eventName,
      eventTimestamp,
      alertInventoryId,
      transactionId,
      eventOffsetId,
      blockId,
    };

    try {
      logger.debug('Fetching previous alert state', logContext);
      const previousAlertState = await AlertService.getPreviousAlertState({
        siteId,
        alertInventoryId,
      });

      if (previousAlertState?.timestamp) {
        const currentTimestamp = moment.tz(eventTimestamp, 'UTC');
        const prevTimestamp = moment.tz(previousAlertState.timestamp, 'UTC');

        if (currentTimestamp.isBefore(prevTimestamp) || currentTimestamp.isSame(prevTimestamp)) {
          logger.info('Duplicate or out-of-order event detected', { ...logContext, prevTimestamp: prevTimestamp.toISOString(), currentTimestamp: currentTimestamp.toISOString() });
          return;
        }
      }

      if (eventName === 'OCCURRED') {
        await AlertService.processOccurredEvent(this.alertEventPacket);
        return;
      }

      if (eventName === 'RESOLVED') {
        await AlertService.processResolvedEvent(this.alertEventPacket);
        return;
      }

      logger.warn('Unknown event type, ignoring', logContext);
    } catch (error) {
      logger.error(`Failed to process stateful alert event: ${error.message}`, logContext);
      throw error;
    }
  }
  async processNotificationEvent(event) {}
}
