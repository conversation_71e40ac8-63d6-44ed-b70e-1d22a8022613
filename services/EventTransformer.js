import <PERSON><PERSON> from 'joi';
import { v4 as uuidv4 } from 'uuid';
import { getAlertInventoryByEventId } from '../repository/Alert.repository.js';
import logger from '../utils/logger.js';

export default class EventTransformer {
  constructor() {
    this._transformedEvent = {
      transactionId: uuidv4(),
    };
  }

  async transform(eventPayload) {
    this._transformedEvent.eventOffsetId = eventPayload.eventOffsetId;
    const alertInventoryMetaData = await getAlertInventoryByEventId({
      alertEventId: eventPayload.alertId,
      siteId: eventPayload.siteId,
      assetId: eventPayload.deviceInfo?.id,
    });
    if (!alertInventoryMetaData) {
      logger.error('E_ALERT_NOT_REGISTERED', {
        alertId: eventPayload.alertId,
        siteId: eventPayload.siteId,
        assetId: eventPayload.deviceInfo?.id,
        alertType: eventPayload.alertType,
        eventName: eventPayload.eventName,
        blockId: eventPayload.blockId,
        eventOffsetId:this._transformedEvent.eventOffsetId,
        transactionId: this._transformedEvent.transactionId,
      });
      return null;
    }
    Object.assign(this._transformedEvent, alertInventoryMetaData);
    if (this._transformedEvent.ALERT_CATEGORY === 'cpa')
      Object.assign(this._transformedEvent, this._handleCPATypeAlert(eventPayload));
    if (this._transformedEvent.ALERT_CATEGORY === 'recipe')
      Object.assign(this._transformedEvent, this._handleRecipeAlert(eventPayload));
    Object.assign(this._transformedEvent, this._genericHandler(eventPayload));
    if (this._isValidEvent()) return this._transformedEvent;
    return null;
  }

  _handleCPATypeAlert(eventPayload) {
    let transformedEvent;
    transformedEvent = {
      alertId: eventPayload.alertId,
      eventName: eventPayload.eventName,
      data: eventPayload.data,
      deviceInfo: eventPayload.deviceInfo,
      eventTimestamp: eventPayload.timestamp,
      misc: eventPayload.misc || {
        alertSource: eventPayload.alertSource,
        alertObserver: eventPayload.alertObserver,
        alertObserverService: eventPayload.alertObserverService,
      },
    };
    return transformedEvent;
  }

  _handleRecipeAlert(eventPayload) {
    let transformedEvent;
    transformedEvent = {
      alertId: eventPayload.alertId,
      eventName: eventPayload.eventName,
      data: eventPayload.data,
      deviceInfo: eventPayload.deviceInfo,
      eventTimestamp: eventPayload.timestamp,
      blockId: eventPayload.blockId,
      misc: eventPayload.misc,
    };
    return transformedEvent;
  }

  _genericHandler() {}

  _isValidEvent() {
    const schema = Joi.object({
      alertInventoryId: Joi.number().required(),
      alertDescription: Joi.string().allow('', null),
      alertName: Joi.string().required(),
      alertTemplateId: Joi.number().required(),
      alertId: Joi.string().required(),
      siteId: Joi.string().required(),
      eventName: Joi.string().valid('OCCURRED', 'RESOLVED').required(),
      data: Joi.object().allow(null),
      deviceInfo: Joi.object().allow(null),
      eventTimestamp: Joi.date().iso().required(),
      blockId: Joi.string().allow('', null),
      misc: Joi.object().allow(null),
      transactionId: Joi.string().required(),
      severity: Joi.string().valid('low', 'medium', 'high', 'critical'),
      STATEFUL_ALERT_FLAG: Joi.number().valid(0, 1),
      ALERT_TEMPLATE_CATEGORY: Joi.number().valid(1, 2).required(),
      ALERT_CATEGORY: Joi.string().valid('cpa', 'recipe', 'iot_health').required(),
    }).unknown(true);

    const { error } = schema.validate(this._transformedEvent, {
      stripUnknown: false,
      abortEarly: false,
    });

    if (error) {
      logger.error('Event validation failed', {
        alertId: this._transformedEvent.alertId,
        transactionId: this._transformedEvent.transactionId,
        errors: error.details.map((detail) => ({
          field: detail.path.join('.'),
          message: detail.message,
        })),
      });
      return false;
    }

    return true;
  }
}
