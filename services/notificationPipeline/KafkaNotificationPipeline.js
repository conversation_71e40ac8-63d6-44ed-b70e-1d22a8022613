import kafkaConnection from '../../connections/kafka.js';
import { kafkaConfig } from '../../config/index.js';
import logger from '../../utils/logger.js';

export default class KafkaNotificationPipeline {
  constructor() {
    this.NOTIFICATION_QUEUE_TOPIC = kafkaConfig.NOTIFICATION_QUEUE_TOPIC;
  }

  async sendToKafka(notification) {
    try {
      await kafkaConnection.sendMessage(this.NOTIFICATION_QUEUE_TOPIC, notification);
      logger.error('Successfully published notification to Kafka🚀', {
        topic: this.NOTIFICATION_QUEUE_TOPIC,
        notification,
      });
    } catch (error) {
      logger.error(`Failed to publish to Kafka notification queue: ${error.message}`, {
        topic: this.NOTIFICATION_QUEUE_TOPIC,
        notification,
      });
      throw error;
    }
  }
}
