import logger from '../../utils/logger.js';

export default class NotificationEventPipeline {
  constructor(kafkaService) {
    this.kafkaService = kafkaService;
  }

  async publish(notification) {
    try {
      await this.kafkaService.sendToKafka(notification);
    } catch (error) {
      logger.error('Failed to publish notification', {
        error: error.message,
        notification,
      });
      throw error;
    }
  }
}
