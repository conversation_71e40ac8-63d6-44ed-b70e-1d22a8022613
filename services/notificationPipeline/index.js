import KafkaNotificationPipeline from './KafkaNotificationPipeline.js';
import NotificationEventPipeline from './NotificationEventPipeline.js';

export async function sendNotification(notification) {
  const kafkaNotificationService = new KafkaNotificationPipeline();
  const notificationPipeline = new NotificationEventPipeline(kafkaNotificationService);
  await notificationPipeline.publish(notification);
}
