import redisClient from '../connections/redis.js';
import { isEmpty } from '../helpers/index.js';
import { fetchPreviousAlertStateFromDatabase } from '../repository/Alert.repository.js';
import {
  checkActiveIncident,
  createAlertIncident,
  lockIncidentById,
  resolveAlertIncident,
  updateIncidentBlockId,
} from '../repository/AlertIncident.repository.js';
import { ingestIncidentToInflux } from '../repository/IncidentInflux.repository.js';
import logger from '../utils/logger.js';
import moment from 'moment-timezone';
import PostgresConnection from '../connections/postgres.js';
import { withLock } from '../utils/lock.js';
import { sendNotification } from './notificationPipeline/index.js';
import { emitAlertMetric } from '../utils/metrics.js';

export default class AlertService {
  static async getPreviousAlertState({ siteId, alertInventoryId, pgCon = null }) {
    try {
      const alertStateKey = `alert_state:${siteId}:${alertInventoryId}`;
      const redisState = await redisClient.hGetAll(alertStateKey);

      if (!isEmpty(redisState)) {
        return {
          siteId: redisState.siteId || null,
          alertInventoryId: redisState.alertInventoryId ? parseInt(redisState.alertInventoryId) : null,
          incidentId: redisState.incidentId ? parseInt(redisState.incidentId) : null,
          eventName: redisState.eventName || null,
          timestamp: redisState.timestamp || null,
          blockId: redisState.blockId || null,
        };
      }

      const lastIncident = await fetchPreviousAlertStateFromDatabase(alertInventoryId, pgCon);
      if (lastIncident) {
        const dbState = {
          siteId,
          alertInventoryId: parseInt(alertInventoryId),
          incidentId: lastIncident.event_name === 'OCCURRED' ? lastIncident.incident_id : null,
          eventName: lastIncident.event_name,
          timestamp: lastIncident.timestamp,
          blockId: lastIncident.block_id || null,
        };
        await AlertService.saveAlertState(dbState);
        return dbState;
      }

      return null;
    } catch (error) {
      throw new Error(`Failed to fetch alert state: ${error.message}`);
    }
  }

  static async saveAlertState({ siteId, alertInventoryId, incidentId = null, eventName, timestamp, blockId = null }) {
    try {
      const alertStateKey = `alert_state:${siteId}:${alertInventoryId}`;

      const stateData = {
        siteId: String(siteId),
        alertInventoryId: String(alertInventoryId),
        incidentId: incidentId != null ? String(incidentId) : '',
        eventName: String(eventName),
        timestamp: timestamp != null ? String(timestamp) : '',
        blockId: blockId != null ? String(blockId) : '',
      };

      await redisClient.hSet(alertStateKey, stateData);
      await redisClient.expire(alertStateKey, 3600); // 1h TTL
    } catch (error) {
      throw new Error(`Failed to save alert state: ${error.message}`);
    }
  }

  static async _incrementAlertOccurrence({ siteId, incidentId, timestamp }) {
    const occurrenceKey = `alert_occurrence:${siteId}:incident:${incidentId}`;

    await redisClient.incr(`${occurrenceKey}:count`);
    await redisClient.set(`${occurrenceKey}:ts`, timestamp);

    await redisClient.expire(`${occurrenceKey}:count`, 30 * 24 * 60 * 60); // 30d TTL
    await redisClient.expire(`${occurrenceKey}:ts`, 30 * 24 * 60 * 60);
  }

  static async _resetAlertOccurrence({ siteId, incidentId }) {
    const occurrenceKey = `alert_occurrence:${siteId}:incident:${incidentId}`;
    await redisClient.del(`${occurrenceKey}:count`, `${occurrenceKey}:ts`);
  }

  static async _getAlertOccurrence({ siteId, incidentId }) {
    const occurrenceKey = `alert_occurrence:${siteId}:incident:${incidentId}`;
    try {
      const [count, recentTs] = await Promise.all([
        redisClient.get(`${occurrenceKey}:count`),
        redisClient.get(`${occurrenceKey}:ts`),
      ]);

      const parsedCount = count ? parseInt(count) : 1;
      return {
        count: Math.max(parsedCount, 1),
        recentTs: recentTs || null,
      };
    } catch (error) {
      logger.error(`Failed to get alert occurrence from redis: ${error.message}`, {
        siteId,
        incidentId,
      });
      return {
        count: 1,
        recentTs: null,
      };
    }
  }

  static async processOccurredEvent(alertEventPacket) {
    const {
      alertInventoryId,
      assetId,
      severity,
      siteId,
      alertTemplateId,
      ALERT_CATEGORY,
      alertId: observer_execution_ref_id,
      eventTimestamp,
      transactionId,
      eventOffsetId,
      blockId,
    } = alertEventPacket;

    const logContext = {
      siteId,
      alertInventoryId,
      severity,
      eventTimestamp,
      eventName: 'OCCURRED',
      observer_execution_ref_id,
      transactionId,
      eventOffsetId,
      blockId,
    };

    const lockKey = `alert_incident_lock:${siteId}:${alertInventoryId}`;
    let pgCon = null;
    let incidentId = null;
    let utcEventTimestamp;

    return await withLock(lockKey, async () => {
      try {
        utcEventTimestamp = moment.tz(eventTimestamp, 'UTC').toISOString();

        // First check Redis for existing state
        const previousAlertState = await AlertService.getPreviousAlertState({
          siteId,
          alertInventoryId,
        });

        // Case 1: Already have incident in Redis
        const existingIncidentId = previousAlertState?.incidentId;
        if (existingIncidentId) {
          // Update database if blockId changed
          if (blockId && previousAlertState.blockId !== blockId) {
            pgCon = await PostgresConnection.beginTransaction();

            try {
              await updateIncidentBlockId({
                id: existingIncidentId,
                block_id: blockId,
                pgCon,
              });

              await pgCon.commit();

              // Update Redis state AFTER successful commit
              try {
                await AlertService.saveAlertState({
                  siteId,
                  alertInventoryId,
                  incidentId: existingIncidentId,
                  eventName: 'OCCURRED',
                  timestamp: utcEventTimestamp,
                  blockId,
                });

                await AlertService._incrementAlertOccurrence({
                  siteId,
                  incidentId: existingIncidentId,
                  timestamp: utcEventTimestamp,
                });

                logger.info('Found existing unresolved incident, updated block_id', {
                  ...logContext,
                  incidentId: existingIncidentId,
                  previousBlockId: previousAlertState.blockId,
                });
              } catch (sideEffectError) {
                logger.error('Failed to update Redis after successful block_id update', {
                  ...logContext,
                  incidentId: existingIncidentId,
                  error: sideEffectError.message
                });
              }
            } catch (error) {
              if (pgCon) await pgCon.rollback();
              logger.error('Failed to update incident block_id in database', {
                ...logContext,
                incidentId: existingIncidentId,
                error: error.message,
              });
            }
          } else {
            // No database update needed, just update Redis
            try {
              await AlertService.saveAlertState({
                siteId,
                alertInventoryId,
                incidentId: existingIncidentId,
                eventName: 'OCCURRED',
                timestamp: utcEventTimestamp,
                blockId: previousAlertState.blockId,
              });

              await AlertService._incrementAlertOccurrence({
                siteId,
                incidentId: existingIncidentId,
                timestamp: utcEventTimestamp,
              });

              logger.debug('Found existing unresolved incident, block_id unchanged', {
                ...logContext,
                incidentId: existingIncidentId,
              });
            } catch (sideEffectError) {
              logger.error('Failed to update Redis for existing incident', {
                ...logContext,
                incidentId: existingIncidentId,
                error: sideEffectError.message
              });
            }
          }
          return;
        }

        // Case 2 & 3: Check database then create if needed
        pgCon = await PostgresConnection.beginTransaction();

        try {
          const existingIncident = await checkActiveIncident({
            alert_inventory_id: alertInventoryId,
            pgCon,
          });

          if (existingIncident) {
            // Case 2: Incident exists in DB but not in Redis
            incidentId = existingIncident.id;

            if (blockId && existingIncident.block_id !== blockId) {
              await updateIncidentBlockId({
                id: existingIncident.id,
                block_id: blockId,
                pgCon,
              });

              logger.info('Unresolved incident exists, updated block_id', {
                ...logContext,
                existingIncidentId: existingIncident.id,
                existingIncidentOccurredAt: existingIncident.issue_occurred_at,
                previousBlockId: existingIncident.block_id,
              });
            } else {
              logger.info('Unresolved incident exists, block_id unchanged', {
                ...logContext,
                existingIncidentId: existingIncident.id,
                existingIncidentOccurredAt: existingIncident.issue_occurred_at,
              });
            }

            await pgCon.commit();

            try {
              await AlertService.saveAlertState({
                siteId,
                alertInventoryId,
                incidentId: existingIncident.id,
                eventName: 'OCCURRED',
                timestamp: utcEventTimestamp,
                blockId,
              });

              await AlertService._incrementAlertOccurrence({
                siteId,
                incidentId: existingIncident.id,
                timestamp: utcEventTimestamp,
              });
            } catch (sideEffectError) {
              logger.error('Failed to update Redis for existing incident', {
                ...logContext,
                incidentId: existingIncident.id,
                error: sideEffectError.message
              });
            }
          } else {
            // Case 3: Create new incident
            incidentId = await createAlertIncident({
              alert_inventory_id: alertInventoryId,
              issue_occurred_at: utcEventTimestamp,
              block_id: blockId,
              pgCon,
            });

            logger.info('New Alert incident created', {
              ...logContext,
              incidentId,
            });

            await pgCon.commit();

            try {
              await AlertService.saveAlertState({
                siteId,
                alertInventoryId,
                incidentId,
                eventName: 'OCCURRED',
                timestamp: utcEventTimestamp,
                blockId,
              });

              await AlertService._incrementAlertOccurrence({
                siteId,
                incidentId,
                timestamp: utcEventTimestamp,
              });

              const notificationPayload = {
                incidentId,
                alertInventoryId,
                eventName: 'OCCURRED',
                assetId,
                siteId,
                severity,
                alertTemplateId,
                ALERT_CATEGORY,
                observer_execution_ref_id,
                timestamp: utcEventTimestamp,
                transactionId,
              };
              await sendNotification(notificationPayload);

              const metricPayload = {
                metric_type: 'ALERT_EVENT',
                incident_id: incidentId,
                alert_inventory_id: alertInventoryId,
                event_name: 'OCCURRED',
                asset_id: assetId,
                site_id: siteId,
                severity,
                alert_template_id: alertTemplateId,
                alert_type: ALERT_CATEGORY,
                observer_execution_ref_id,
                parameter_values: JSON.stringify(alertEventPacket.data || {}),
                alert_name: alertEventPacket.alertName,
                alert_description: alertEventPacket.alertDescription,
                event_timestamp: utcEventTimestamp,
                result: 1,
                transaction_id: transactionId,
              };
              await emitAlertMetric(metricPayload);

              await ingestIncidentToInflux({
                siteId,
                alertInventoryId,
                incidentId,
                observer_execution_ref_id,
                timestamp: utcEventTimestamp,
                result: 1,
              });
            } catch (sideEffectError) {
              logger.error('Failed to process side effects after successful commit', {
                ...logContext,
                incidentId,
                error: sideEffectError.message,
              });
            }
          }
        } catch (error) {
          if (pgCon) {
            await pgCon.rollback();
          }
          logger.error(`Failed to process occurred event ${error.message}`, logContext);
          throw error;
        }
      } catch (error) {
        logger.error(`E_FAILED_TO_PROCESS_OCCURRED_EVENT ${error.message}`, logContext);
        throw error;
      }
    });
  }

  static async processResolvedEvent(alertEventPacket) {
    const {
      alertInventoryId,
      assetId,
      severity,
      siteId,
      alertTemplateId,
      ALERT_CATEGORY,
      alertId: observer_execution_ref_id,
      eventTimestamp,
      transactionId,
      blockId,
    } = alertEventPacket;

    const logContext = {
      siteId,
      alertInventoryId,
      eventTimestamp,
      severity,
      eventName: 'RESOLVED',
      observer_execution_ref_id,
      blockId,
    };

    const lockKey = `alert_incident_lock:${siteId}:${alertInventoryId}`;
    let pgCon = null;
    let utcEventTimestamp;
    let incidentId = null;
    let lockedIncident = null;

    return await withLock(lockKey, async () => {
      try {
        utcEventTimestamp = moment.tz(eventTimestamp, 'UTC').toISOString();

        const previousAlertState = await AlertService.getPreviousAlertState({
          siteId,
          alertInventoryId,
        });

        incidentId = previousAlertState?.incidentId;
        if (!incidentId) {
          logger.debug('No ongoing incident found', logContext);
          return;
        }

        const finalOccurrenceData = await AlertService._getAlertOccurrence({
          siteId,
          incidentId,
        });

        pgCon = await PostgresConnection.beginTransaction();

        try {
          lockedIncident = await lockIncidentById({
            id: incidentId,
            pgCon,
          });

          if (!lockedIncident) {
            logger.error('Incident not found or already resolved', logContext);
            await pgCon.rollback();
            return;
          }

          await resolveAlertIncident({
            id: incidentId,
            issue_resolved_at: utcEventTimestamp,
            occurred_event_count: finalOccurrenceData.count,
            recent_occurred_event_ts: finalOccurrenceData.recentTs,
            block_id: blockId,
            pgCon,
          });

          await pgCon.commit();

          try {
            await AlertService.saveAlertState({
              siteId,
              alertInventoryId,
              incidentId: null,
              eventName: 'RESOLVED',
              timestamp: utcEventTimestamp,
              blockId,
            });

            await AlertService._resetAlertOccurrence({ siteId, incidentId });

            const notificationPayload = {
              incidentId,
              alertInventoryId,
              eventName: 'RESOLVED',
              assetId,
              siteId,
              severity,
              alertTemplateId,
              ALERT_CATEGORY,
              observer_execution_ref_id,
              timestamp: utcEventTimestamp,
              timestampOccurred: lockedIncident?.issue_occurred_at ? moment.tz(lockedIncident.issue_occurred_at, 'UTC').toISOString() : null,
              transactionId,
            };
            await sendNotification(notificationPayload);

            const metricPayload = {
              metric_type: 'ALERT_EVENT',
              incident_id: incidentId,
              alert_inventory_id: alertInventoryId,
              event_name: 'RESOLVED',
              asset_id: assetId,
              site_id: siteId,
              severity,
              alert_template_id: alertTemplateId,
              alert_type: ALERT_CATEGORY,
              observer_execution_ref_id,
              parameter_values: JSON.stringify(alertEventPacket.data || {}),
              alert_name: alertEventPacket.alertName,
              alert_description: alertEventPacket.alertDescription,
              event_timestamp: utcEventTimestamp,
              result: 0,
              transaction_id: transactionId,
            };
            await emitAlertMetric(metricPayload);

            await ingestIncidentToInflux({
              siteId,
              alertInventoryId,
              incidentId,
              observer_execution_ref_id,
              timestamp: utcEventTimestamp,
              result: 0,
            });
          } catch (sideEffectError) {
            logger.error('Failed to process side effects after successful commit for RESOLVED event', {
              ...logContext,
              incidentId,
              error: sideEffectError.message,
            });
          }
        } catch (error) {
          if (pgCon) {
            await pgCon.rollback();
          }
          logger.error(`Failed to process resolved event ${error.message}`, logContext);
          throw error;
        }
      } catch (error) {
        logger.error(`E_FAILED_TO_PROCESS_RESOLVED_EVENT ${error.message}`, logContext);
        throw error;
      }
    });
  }
}
