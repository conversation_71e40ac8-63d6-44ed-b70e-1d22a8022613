import 'dotenv/config';

import logger from './utils/logger.js';
import PostgresConnection from './connections/postgres.js';
import {
  createAlertIncident,
  checkActiveIncident,
  updateIncidentBlockId,
  lockIncidentById,
  resolveAlertIncident
} from './repository/AlertIncident.repository.js';
import { fetchPreviousAlertStateFromDatabase } from './repository/Alert.repository.js';
import redisClient from './connections/redis.js';
import AlertService from './services/Alert.service.js';

const getTestData = () => ({
  alertInventoryId: Math.floor(Math.random() * 1000) + 1,
  siteId: 'test-site-' + Math.floor(Math.random() * 100),
  timestamp: new Date().toISOString(),
  blockId: 'block-' + Math.floor(Math.random() * 1000),
  newBlockId: 'block-' + Math.floor(Math.random() * 1000) + 1000,
});

async function testCreateIncidentWithBlockId() {
  const { alertInventoryId, siteId, timestamp, blockId } = getTestData();
  let pgCon = null;
  let incidentId = null;

  try {
    pgCon = await PostgresConnection.beginTransaction();

    incidentId = await createAlertIncident({
      alert_inventory_id: alertInventoryId,
      issue_occurred_at: timestamp,
      block_id: blockId,
      pgCon,
    });

    await pgCon.commit();

    // Verify in database
    const dbState = await fetchPreviousAlertStateFromDatabase(alertInventoryId);

    if (!dbState) {
      throw new Error('Failed to find incident in database after commit');
    }

    logger.info('Incident created successfully', {
      incidentId: dbState.incident_id,
      eventName: dbState.event_name,
      blockId: dbState.block_id
    });

    await AlertService.saveAlertState({
      siteId,
      alertInventoryId,
      incidentId,
      eventName: 'OCCURRED',
      timestamp,
      blockId,
    });

    if (dbState.block_id !== blockId) {
      throw new Error(`blockId mismatch. Expected: ${blockId}, Got: ${dbState.block_id}`);
    }

    logger.info('Test: Create incident with blockId - PASSED', { incidentId, blockId });
    return { incidentId, alertInventoryId, siteId, blockId };

  } catch (error) {
    if (pgCon) await pgCon.rollback();
    logger.error('Test: Create incident with blockId - FAILED', { error: error.message });
    throw error;
  }
}

// Test function for updating an incident's blockId
async function testUpdateIncidentBlockId(incidentData) {
  const { incidentId, alertInventoryId, siteId } = incidentData;
  const { newBlockId, timestamp } = getTestData();
  let pgCon = null;

  try {
    const currentState = await AlertService.getPreviousAlertState({
      siteId,
      alertInventoryId
    });

    pgCon = await PostgresConnection.beginTransaction();

    await updateIncidentBlockId({
      id: incidentId,
      block_id: newBlockId,
      pgCon,
    });

    await pgCon.commit();

    const dbState = await fetchPreviousAlertStateFromDatabase(alertInventoryId);

    if (!dbState) {
      throw new Error('Failed to find incident in database after update');
    }

    await AlertService.saveAlertState({
      siteId,
      alertInventoryId,
      incidentId,
      eventName: 'OCCURRED',
      timestamp,
      blockId: newBlockId,
    });

    const updatedState = await AlertService.getPreviousAlertState({
      siteId,
      alertInventoryId
    });

    logger.info('Incident blockId updated', {
      incidentId,
      oldBlockId: currentState.blockId,
      newBlockId: updatedState.blockId
    });

    if (dbState.block_id !== newBlockId) {
      throw new Error(`blockId not updated in DB. Expected: ${newBlockId}, Got: ${dbState.block_id}`);
    }

    if (updatedState.blockId !== newBlockId) {
      throw new Error(`blockId not updated in Redis. Expected: ${newBlockId}, Got: ${updatedState.blockId}`);
    }

    logger.info('Test: Update incident blockId - PASSED', { incidentId, newBlockId });
    return { ...incidentData, blockId: newBlockId };

  } catch (error) {
    if (pgCon) await pgCon.rollback();
    logger.error('Test: Update incident blockId - FAILED', { error: error.message });
    throw error;
  }
}

// Test function for resolving an incident with blockId
async function testResolveIncidentWithBlockId(incidentData) {
  const { incidentId, alertInventoryId, siteId, blockId } = incidentData;
  const { timestamp } = getTestData();
  let pgCon = null;

  try {
    pgCon = await PostgresConnection.beginTransaction();

    const lockedIncident = await lockIncidentById({
      id: incidentId,
      pgCon,
    });

    if (!lockedIncident) {
      throw new Error('Failed to lock incident for resolution');
    }

    await resolveAlertIncident({
      id: incidentId,
      issue_resolved_at: timestamp,
      occurred_event_count: 1,
      recent_occurred_event_ts: timestamp,
      block_id: blockId, // Keep same blockId
      pgCon,
    });

    await pgCon.commit();

    const dbState = await fetchPreviousAlertStateFromDatabase(alertInventoryId);

    if (!dbState) {
      throw new Error('Failed to find incident in database after resolution');
    }

    await AlertService.saveAlertState({
      siteId,
      alertInventoryId,
      incidentId: null, // No active incident after resolution
      eventName: 'RESOLVED',
      timestamp,
      blockId,
    });

    logger.info('Incident resolved successfully', {
      incidentId,
      eventName: dbState.event_name,
      blockId: dbState.block_id
    });

    if (dbState.event_name !== 'RESOLVED') {
      throw new Error(`Incident not properly resolved. Expected: RESOLVED, Got: ${dbState.event_name}`);
    }

    if (dbState.block_id !== blockId) {
      throw new Error(`blockId changed during resolution. Expected: ${blockId}, Got: ${dbState.block_id}`);
    }

    logger.info('Test: Resolve incident with blockId - PASSED');
    return true;

  } catch (error) {
    if (pgCon) await pgCon.rollback();
    logger.error('Test: Resolve incident with blockId - FAILED', { error: error.message });
    throw error;
  }
}

// Test function to verify Redis state matches database
async function testRedisStateMatchesDatabase(incidentData) {
  const { alertInventoryId, siteId } = incidentData;

  try {
    const redisState = await AlertService.getPreviousAlertState({
      siteId,
      alertInventoryId
    });

    const dbState = await fetchPreviousAlertStateFromDatabase(alertInventoryId);

    if (!dbState) {
      throw new Error('Failed to find incident in database for Redis comparison');
    }

    const dbFormattedState = {
      siteId,
      alertInventoryId: parseInt(alertInventoryId),
      incidentId: dbState.event_name === 'OCCURRED' ? dbState.incident_id : null,
      eventName: dbState.event_name,
      timestamp: dbState.timestamp,
      blockId: dbState.block_id,
    };

    // Compare the states
    const statesMatch =
      redisState.eventName === dbFormattedState.eventName &&
      redisState.blockId === dbFormattedState.blockId &&
      (
        (redisState.incidentId === null && dbFormattedState.incidentId === null) ||
        (redisState.incidentId === dbFormattedState.incidentId)
      );

    if (!statesMatch) {
      logger.error('State mismatch', {
        redisState,
        dbFormattedState
      });
      throw new Error('Redis state does not match database state');
    }

    logger.info('Redis state verified to match database state');
    logger.info('Test: Verify state consistency - PASSED');
    return true;
  } catch (error) {
    logger.error('Test: Redis state verification - FAILED', { error: error.message });
    throw error;
  }
}

// Execute all tests in sequence
async function runAllTests() {
  try {
    const incidentData = await testCreateIncidentWithBlockId();

    const updatedIncidentData = await testUpdateIncidentBlockId(incidentData);

    await testRedisStateMatchesDatabase(updatedIncidentData);

    await testResolveIncidentWithBlockId(updatedIncidentData);

    await testRedisStateMatchesDatabase(updatedIncidentData);

    logger.info('All blockId feature tests PASSED!');
    return true;
  } catch (error) {
    logger.error('blockId feature tests FAILED', { error: error.message });
    return false;
  }
}

// Clean up test data
async function cleanupTests(successful) {
  try {
    const testKeys = await redisClient.keys('alert_state:test-site-*');

    if (testKeys.length > 0) {
      await redisClient.del(testKeys);
      logger.info('Test data cleaned up from Redis', { keysRemoved: testKeys.length });
    }

    logger.info(`Integration tests ${successful ? 'completed successfully' : 'failed'}`);
  } catch (error) {
    logger.error('Error during test cleanup', { error: error.message });
  } finally {
    await redisClient.quit();
  }
}

(async function main() {
  let successful = false;
  try {
    new PostgresConnection();

    successful = await runAllTests();

  } catch (error) {
    logger.error('Unexpected error during tests', { error: error.message, stack: error.stack });
    successful = false;
  } finally {
    await cleanupTests(successful);
    process.exit(successful ? 0 : 1);
  }
})();
