const SiteBootstrapFacade = require("../../services/siteBootstrap/site.bootstrap.service");

module.exports = {
  friendlyName: "dejoule-site-bootstrap-data",
  description: "Fetches and returns the necessary data to bootstrap the site details view",

  inputs: {
    siteId: {
      type: "string",
      description: "The ID of the site for which the bootstrap data is being requested",
      required: true,
      example: "gob-coi",
    },
    _userMeta: {
      type: "json",
      description: "Auto attached by isAuthorized policy from jwt token",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      statusCode: 500,
      description: "[siteBootstrap > dejoule-site-bootstrap-data] Server Error!",
    },
    notFound: {
      statusCode: 404,
      description: "[siteBootstrap > dejoule-site-bootstrap-data] Site Not Found!",
    },
    unauthorized: {
      responseType: "unauthorized",
      statusCode: 403,
      description: "siteBootstrap > dejoule-site-bootstrap-data] Unauthorized!",
    },
    badRequest: {
      responseType: "badRequest",
      statusCode: 400,
      description: "[siteBootstrap > dejoule-site-bootstrap-data] Bad Request!",
    },
    success: {
      statusCode: 200,
      description: "[siteBootstrap > dejoule-site-bootstrap-data] Success",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        _userMeta: { id: userId, _role: role },
      } = inputs;

      const siteBootstrapInstance = new SiteBootstrapFacade(userId, role, siteId);
      const response = await siteBootstrapInstance.bootstrap();
      return exits.success(response);
    } catch (error) {
      sails.log.error("[siteBootstrap > dejoule-site-bootstrap-data] Error!");
      sails.log.error(error);
      switch (error.code) {
        case "E_SITE_NOT_FOUND":
          return exits.notFound({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
