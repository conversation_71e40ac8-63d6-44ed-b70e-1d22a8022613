const siteService = require('../../services/site/site.service');
const util = require('../../utils/site/edit-site-info.util');
const userSiteMapPublic = require('../../services/userSiteMap/userSiteMap.public');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');
const utils = require('../../utils/site/utils');

module.exports = {
  friendlyName: 'editSiteInfo',
  description: 'Edit a pre configured site.',
  example: [
    'curl -X PUT "http://localhost:1337/site/v2/mgch',
  ],

  inputs: {
    siteName: {
      type: 'string',
      description: 'site name',
    },
    regionCount: {
      type: 'number',
      description: 'total number of region',
    },
    planningDocUrl: {
      type: 'string',
      description: 'planning doc s3 signed url if exist',
    },
    circuitCount: {
      type: 'number',
      description: 'circuit count if exist',
    },
    timezone: {
      type: 'string',
      example: 'Asia/Kolkata',
      description: 'Timezone of the site',
    },
    unitCost: {
      type: 'number',
      description: 'cost per unit in rupees',
    },
    industryType: {
      type: 'string',
      example: 'ibms',
      description: 'site belong to which all industry',
      custom: utils.isValidindustryType,
    },
    _userMeta: {
      type: 'json',
      description: 'site name',
    },
  },

  exits: {
    notFound: {
      responseType: 'notFound',
      description: 'SiteId not found.',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[action > editSiteInfo] Bad Request!',
    },
    invalidCircuitCount: {
      statusCode: 422,
      description: 'validation failed at circuit count update'
    }
  },

  async fn(inputs, exits) {
    try {
      const { _userMeta } = inputs;
      delete inputs._userMeta;
      const siteRecord = await siteService.findOne({ siteId: _userMeta._site });
      if (!siteRecord) {
        return exits.notFound({ problems: [`site id "${_userMeta._site}" doesn't exist`] });
      }
      if (siteRecord.regions && !util.validateCircuitCount(inputs.circuitCount, siteRecord.regions)) {
        return exits.invalidCircuitCount({ problems: [`You can not update the circuit lesser than the allocated circuit id`] });
      }
      const object = util.generateDTO(inputs);
      await siteService.update({ siteId: _userMeta._site }, object);
      const userList = await Users.find();
      for (const user of userList) {
        await userSiteMapPublic.invalidateUserSiteIds(user?.userId);
      }

      const auditPayload = {
        event_name: 'state_update_site',
        user_id: _userMeta?.id,
        site_id: _userMeta._site,
        asset_id: 'site-' + _userMeta._site,
        req: this.req,
        prev_state: siteRecord,
        curr_state: object,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success(Object.assign(object, { siteId: _userMeta._site }));
    } catch (error) {
      this.res.serverError();
    }
  },
};
