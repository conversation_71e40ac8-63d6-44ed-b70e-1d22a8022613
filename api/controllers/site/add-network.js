const siteService = require('../../services/site/site.service');
const util = require('../../utils/site/add-network.util.js');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.service');

module.exports = {
  friendlyName: 'addNetwork',
  description: 'Adds a network in a preconfigured site.',
  example: ['curl -X GET "http://127.0.0.1:1337/m2/site/v2/sjo-del/network'],

  inputs: {
    siteId: {
      type: 'string',
      example: 'sjo-del',
      required: true,
    },
    vendor: {
      type: 'string',
      required: true,
      example: 'smartjoules'
    },
    networkType: {
      type: 'string',
      required: true,
      isIn: ['standalone', 'network'],
    },
    _userMeta: {
      type: 'json',
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[site > addNetwork] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[site > addNetwork] Bad Request!',
    },
  },

  async fn({
    siteId,
    networkType,
    vendor
  }, exits) {
    try {
      const siteDetail = await siteService.findOne({ siteId });
      if (!siteDetail) {
        return exits.notFound({ problems: [`site id "${siteId}" doesn't exist`] });
      }
      let { networks } = siteDetail;
      networks = networks || {};
      const networkId = util.generateNetworkId(siteDetail, {
        type: networkType,
        vendor,
      });
      networks[networkId] = [];
      await siteService.update(
        { siteId },
        {
          networks: JSON.stringify(networks),
        },
      );
      await notifyJouleTrackPublicRoom(
        siteId, 'sites', {
          event: 'update',
          data: siteDetail
        }
      );
      return exits.success({ networks });
    } catch (error) {
      sails.log.error('[site > addNetwork] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
