const siteService = require('../../services/site/site.service');
const utils = require('../../utils/site/utils');
const userConfigService = require('../../services/userConfig/userConfig.public');
const userSiteMapPublic = require('../../services/userSiteMap/userSiteMap.public');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'addNewSite',
  description: 'Route to create a new site.',
  example: [
    `curl -X POST "http://localhost:1337/site/v2/`,
  ],

  inputs: {
    siteName: {
      type: 'string',
      example: 'Mahatma Gandhi Cancer Hospital',
      description: 'Name of site',
      required: true
    },
    chainName: {
      type: 'string',
      example: 'Fortis Health care',
      description: 'name of chain'
    },
    latitude: {
      type: 'string',
      example: '38.8951',
      description: 'latitude of the site',
      required: true
    },
    longitude: {
      type: 'string',
      example: '-77.0364',
      description: 'longitude of the site',
      required: true
    },
    location: {
      type: 'string',
      example: 'delhi',
      description: 'site city location',
      required: true
    },
    timezone: {
      type: 'string',
      example: 'Asia/Kolkata',
      description: 'Timezone of the site',
      required: true
    },
    bussinessModel: {
      type: 'string',
      example: 'CAAS',
      description: 'bussiness model',
      isIn: utils.BUSINESS_MODEL
    },
    industryType: {
      type: 'string',
      example: 'ibms',
      description: 'site belong to which all industry',
      custom: utils.isValidindustryType,
      required: true,
    },
    performancekpi: {
      type: {},
      example: { 'conselec': 10 },
      description: 'KPI on which site performance will be measured',
      custom: utils.isValidperformancekpi,
    },
    unitCost: {
      type: 'number',
      description: 'electricity cost per unit',
      required: true,
    },
    _userMeta: {
      type: 'json',
      description: 'site name'
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[datadevice > addNewSite] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[datadevice > addNewSite] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[datadevice > addNewSite] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let {
        siteName,
        location,
        _userMeta: { id: userId },
      } = inputs;
      let siteId = await siteService.findAvailableSiteId(siteName, location);

      /**Create site*/
      let site = {
        ...inputs,
        siteId
      };
      await siteService.create(site);

      /**Give access to developers**/
      userConfigService.giveAccessToDevelopers(siteId)
        .catch(error => {
          sails.log.error('[addNewSite] Error giving access to developers! : ', error);
        });

      /**Remove User SiteMap Cache*/
      const userList = await Users.find();
      for (const user of userList) {
        await userSiteMapPublic.invalidateUserSiteIds(user?.userId);
      }

      const auditPayload = {
        event_name: 'state_create_site',
        user_id: userId,
        site_id: siteId,
        asset_id: 'site-' + siteId,
        req: this.req,
        prev_state: null,
        curr_state: site,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success({
        status: 'done',
        siteId
      });

    } catch (error) {
      sails.log.error('[site > addNewSite] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
