const siteService = require("../../services/site/site.service");
const util = require("../../utils/site/add-region.util");
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: "addRegion",
  description: "",
  example: [`curl -X POST "http://localhost:1337/site/v2/area/:areaId/region`],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      description: "",
    },
    name: {
      type: "string",
      required: true,
      description: "name of region",
    },
    areaId: {
      type: "string",
      required: true,
      description: "area id",
    },
    circuitId: {
      type: "number",
      required: false,
      description:
        "for some site we have to store the circuitId along with regions",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[site > addRegion] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[site > addRegion] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[site > addRegion] resource not found",
    },
    invalidCircuitId: {
      statusCode: 422,
      description: "circuitId value is not valid",
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { _userMeta, name, areaId, circuitId } = inputs;
      let { _site: siteId, id: userId } = _userMeta;
      let siteData = await siteService.findOne({ siteId });
      if (!siteData)
        return exits.notFound({ problems: [`siteId "${siteId}" not found`] });

      let { areas, regions } = siteData;
      if (!areas) {
        areas = {};
      }
      if (!regions) {
        regions = {};
      }
      if (!areas[areaId]) {
        return exits.notFound({ problems: [`area id "${areaId}" not found`] });
      }

      const regionNames = Object.keys(regions).map((it) => regions[it].name);

      if (regionNames.indexOf(name) !== -1)
        return exits.badRequest({
          problems: [
            `A region by name of "${name}" already exists in areaId "${areaId}". Please enter other unique region name`,
          ],
        });
      let regionId = util.generateRegionId(name);
      if (regions[regionId])
        return exits.badRequest({
          problems: [`try again`],
          message:`try again`
        });

      regions[regionId] = {
        name: name,
        area: areaId,
        controller: [],
      };

      //for storing circuitId in KG hospital
      if (circuitId !== undefined && !isNaN(circuitId)) {
        if (!siteData.hasOwnProperty("circuitCount")) {
          return exits.invalidCircuitId({
            problems: [
              `Unable to store the circuitId because circuitCount for this site not exist. Please add circuitCount in edit site first and then add this region with circuitId`,
            ],
          });
        }
        if (circuitId > siteData.circuitCount) {
          return exits.invalidCircuitId({
            problems: [
              `Unable to store the circuitId. circuitId should be less than circuitCount`,
            ],
          });
        }
        regions[regionId].circuitId = parseInt(circuitId);
      }
      areas[areaId].regions.push(regionId);
      await Sites.update(
        { siteId },
        {
          areas: JSON.stringify(areas),
          regions: JSON.stringify(regions),
        }
      );
      const DTO = {
        name: regions[regionId].name,
        area: regions[regionId].area,
        controller: regions[regionId].controller,
        circuitId: circuitId,
        regionId: regionId,
      };

      const auditPayload = {
        event_name: "state_create_region",
        user_id: userId,
        site_id: siteId,
        asset_id: 'region-' + regionId,
        req: this.req,
        prev_state: null,
        curr_state: DTO,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success(DTO);
    } catch (error) {
      sails.log.error("[site > addRegion] Error!");
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
