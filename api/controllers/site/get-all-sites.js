
const siteService = require('../../services/site/site.service');

module.exports = {
  friendlyName: 'getAllSites',
  description : 'To be used via Postman manually to fetch all sites.',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[site > getAllSites] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[site > getAllSites] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[site > getAllSites] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let allSites = await siteService.find();
      let siteList = allSites.map(siteConfig => siteConfig.siteId);
      return exits.success(siteList);
    } catch(error) {
      sails.log.error('[site > getAllSites] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
