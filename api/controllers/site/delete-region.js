const siteService = require("../../services/site/site.service");
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: "deleteRegion",
  description: "deleting region and un-map from area",
  example: [
    `curl -X DELETE "http://localhost:1337/site/v2/area/:areaId/region/:regionId`,
  ],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      description: "",
    },
    areaId: {
      type: "string",
      required: true,
    },
    regionId: {
      type: "string",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[site > deleteRegion] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[site > deleteRegion] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[site > deleteRegion] resource not found",
    },
    unprocessableEntity:{
      statusCode:422,
      description:"Can not delete region because controller is mapped with region"
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { regionId, areaId, _userMeta } = inputs;
      let { _site: siteId, id: userId } = _userMeta;
      let siteData = await siteService.findOne({ siteId });
      if (!siteData)
        return exits.notFound({ problems: [`siteId "${siteId}" not found`] });

      let { areas, regions } = siteData;
      if (!areas) {
        areas = {};
      }

      if (!regions) {
        regions = {};
      }

      if (!areas[areaId])
        return exits.notFound({
          problems: [`area "${areaId}" doesn't exist at siteId "${siteId}" `],
        });

      if (!regions[regionId])
        return exits.notFound({
          problems: [`regionId "${regionId}" doesn't exist in  area "${areas[areaId].name}" `],
        });
      if(regions[regionId].controller.length > 0){
        return exits.unprocessableEntity({problems:[`you can not delete this region because multiple controllers are mapped with them. First remove the mapping from of controller from '${regions[regionId].name}' region`]})

      }

      delete regions[regionId];
      let regionIndex = areas[areaId].regions.indexOf(regionId);
      if(regionIndex !== -1){
        areas[areaId].regions.splice(regionIndex,1);
      }

      await siteService.update(
        { siteId },
        {
          areas: JSON.stringify(areas),
          regions:JSON.stringify(regions)
        }
      );

      const auditPayload = {
        event_name: "state_delete_region",
        user_id: userId,
        site_id: siteId,
        asset_id: 'region-' + regions[regionId],
        req: this.req,
        prev_state: regions,
        curr_state: null,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success();
    } catch (error) {
      sails.log.error("[site > deleteRegion] Error!");
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
