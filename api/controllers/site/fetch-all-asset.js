const { getComponentListByDriverAndDeviceType } = require('../../services/component/component.public');
const { getDeviceListByDriverAndDeviceType } = require('../../services/device/device.public');

module.exports = {


  friendlyName: 'Fetch all asset',


  description: '',


  inputs: {
    siteId: {
      type: 'string',
    },
    deviceType: {
      type: 'string',
    },
    driverType: {
      type: 'string',
    }
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[site > fetch-all-asset] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[site > fetch-all-asset] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[site > fetch-all-asset] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[site > fetch-all-asset] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[site > fetch-all-asset] Asset list fetched successfully'
    }
  },


  fn: async function (inputs, exits) {
    try {
      const { siteId, deviceType, driverType } = inputs;
      const batchAssetList = [];
      batchAssetList.push(getDeviceListByDriverAndDeviceType(siteId, driverType, deviceType), getComponentListByDriverAndDeviceType(siteId, driverType, deviceType));
      const assetList = await Promise.all(batchAssetList);
      return exits.success(assetList.flat());
    } catch (error) {
      return exits.serverError(error)
    }
  }


};
