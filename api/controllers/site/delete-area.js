const siteService = require("../../services/site/site.service");

module.exports = {
  friendlyName: "deleteArea",
  description: "removing an area from site",
  example: [`curl -X DELETE "http://localhost:1337/site/v2/area/:areaId`],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      description: "",
    },
    areaId: {
      type: "string",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[site > deleteArea] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[site > deleteArea] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[site >deleteArea] resource not found",
    },
    unprocessableEntity: {
      statusCode: 422,
      description:
        "Can not delete area because multiple regions is mapped with area",
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { areaId, _userMeta } = inputs;
      let { _site: siteId } = _userMeta;
      let siteData = await siteService.findOne({ siteId });
      if (!siteData)
        return exits.notFound({ problems: [`siteId "${siteId}" not found`] });

      let { areas } = siteData;
      if (!areas) {
        areas = {};
      }

      if (!areas[areaId])
        return exits.success({
          problems: [`area "${areaId}" doesn't exist at siteId "${siteId}" `],
        });
      if (areas[areaId].regions.length > 0) {
        return exits.unprocessableEntity({
          problems: [
            `you can not delete this area because multiple regions are mapped with them. First remove the regions from '${areas[areaId].name}' region`,
          ],
        });
      }

      delete areas[areaId];
      await siteService.update(
        { siteId },
        {
          areas: JSON.stringify(areas),
        }
      );
      return exits.success();
    } catch (error) {
      sails.log.error("[site > deleteArea] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
