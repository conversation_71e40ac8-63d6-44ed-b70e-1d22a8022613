const siteService = require("../../services/site/site.service");
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');
module.exports = {
  friendlyName: "editRegion",
  description: "to update the name of existing region",
  example: [
    `curl -X PUT "http://localhost:1337/m2/site/v2/area/:areaId/region/:regionId`,
  ],
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      description: "",
    },
    areaId: {
      type: "string",
      required: true,
    },
    regionId: {
      type: "string",
      required: false,
    },
    name: {
      type: "string",
      required: false,
    },
    circuitId:{
      type: "string",
      required: false,
    }
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[site > editRegion] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[site > editRegion] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[site >editRegion] resource not found",
    },
    invalidCircuitId: {
      statusCode: 422,
      description: "circuitId value is not valid",
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { name, _userMeta, regionId, circuitId  } = inputs;
      if(name === undefined && circuitId === undefined){
        return exits.badRequest({problems:[`no paramatere found to update`]})
      }
      let { _site: siteId, id: userId } = _userMeta;
      let siteData = await siteService.findOne({ siteId });
      if (!siteData)
        return exits.notFound({ problems: [`siteId "${siteId}" not found`] });

      let { regions } = siteData;
      if (!regions) {
        regions = {};
      }

      if (!regions[regionId])
        return exits.notFound({
          problems: [
            `regionId "${regionId}" doesn't exist at siteId "${siteId}" `,
          ],
        });

      const regionData = JSON.parse(JSON.stringify(regions));
      regions[regionId].name = name ? name : regions[regionId].name;



      //for storing circuitId
      if (circuitId !== undefined && !isNaN(circuitId)) {
        if (!siteData.hasOwnProperty("circuitCount")) {
          return exits.invalidCircuitId({
            problems: [
              `Unable to store the circuitId because circuitCount for this site not exist. Please add circuitCount in edit site first and then add this region with circuitId`,
            ],
          });
        }
        if (circuitId > siteData.circuitCount) {
          return exits.invalidCircuitId({
            problems: [
              `Unable to store the circuitId. circuitId should be less than circuitCount`,
            ],
          });
        }
        regions[regionId].circuitId = parseInt(circuitId);
      }

      await siteService.update(
        { siteId },
        {
          regions: JSON.stringify(regions),
        }
      );
      const DTO = {
        name: regions[regionId].name,
        area: regions[regionId].area,
        controller: regions[regionId].controller,
        circuitId,
        regionId,
      };

      const auditPayload = {
        event_name: "state_update_region",
        user_id: userId,
        site_id: siteId,
        asset_id: 'region-' + regions[regionId],
        req: this.req,
        prev_state: regionData,
        curr_state: DTO,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success(DTO);
    } catch (error) {
      sails.log.error("[site > editRegion] Error!");
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
