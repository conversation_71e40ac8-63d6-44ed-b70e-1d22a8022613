const { getAllDeviceComponentCategories } = require("../../services/site/site.service");
const { inputValidation } = require("../../utils/site/errorHandler.util");

module.exports = {
  friendlyName: 'fetchSiteDeviceComponentDeviceType',
  description: 'fetch all device and component unique device type of configurator table',
  example: [],
  inputs: {
    siteId: {
      type: 'string',
    }
  },
  exits: {
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: '[site >fetchSiteDeviceComponentDeviceType] Server error ',
    },
    forbidden: {
      statusCode: 403,
      description: '[site >fetchSiteDeviceComponentDeviceType] Unauthorized Access',
    },
    notFound: {
      statusCode: 404,
      description: '[site >fetchSiteDeviceComponentDeviceType] not found',
    },
    success: {
      statusCode: 200,
      description: '[site >fetchSiteDeviceComponentDeviceType] Successfully fetched all system',
    },
    badRequest: {
      statusCode: 400,
      description: '[site >fetchSiteDeviceComponentDeviceType] badRequest',
    },
  },
  fn: async function ({siteId}, exits) {
    try {
      inputValidation({siteId});
      const siteDeviceComponents = await getAllDeviceComponentCategories(siteId.replace(/\s+/g, ""));
      return exits.success(siteDeviceComponents);
    } catch (err) {
      if (err.code == 'INPUT_VALIDATION_ERROR') {
        return exits.badRequest({
          message: err.message,
        })
      } else if (err.code == 'E_SITE_NOT_FOUND'){
        return exits.badRequest({
          message: err.message,
        })
      } else {
        sails.log('[site > fetchSiteDeviceComponentDeviceType] ');
        sails.log.error(err);
        return exits.serverError(err);
      }
    }
  },
};
