const siteService = require("../../services/site/site.service");
const util = require("../../utils/site/add-area.util");
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: "addArea",
  description: "",
  example: [`curl -X POST "http://localhost:1337/site/v2/area`],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      description: "",
    },
    name: {
      type: "string",
      required: true,
      description: "name of area",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[site > addArea] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[site > addArea] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[site > addArea] resource not found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { _userMeta, name } = inputs;
      let { _site: siteId, id: userId } = _userMeta;
      let siteData = await siteService.findOne({ siteId });
      if (!siteData)
        return exits.notFound({ problems: [`siteId "${siteId}" not found`] });

      let { areas } = siteData;
      if (!areas) {
        areas = {};
      }

      //List of all existing area name
      const areaNames = Object.keys(areas).map((it) => areas[it].name);

      if (areaNames.indexOf(name) !== -1)
        return exits.badRequest({
          problems: [
            `An area by name of "${name}" already exists at siteId "${siteId}". Please enter new area`,
          ],
        });
      const areaId = util.generateAreaId(name);
      if (areas[areaId])
        return exits.badRequest({
          problems: [`try again`],
        });
      areas[areaId] = {
        name: name,
        regions: [],
      };
      await Sites.update(
        { siteId },
        {
          areas: JSON.stringify(areas),
        }
      );
      const DTO = {
        areaId:areaId,
        name:name
      };

      const auditPayload = {
        event_name: "state_create_site-area",
        user_id: userId,
        site_id: siteId,
        asset_id: 'area-' + areas[areaId],
        req: this.req,
        prev_state: null,
        curr_state: DTO,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success(DTO);
    } catch (error) {
      sails.log.error("[site > addArea] Error!");
      sails.log.error(error);
      return exits.serverError(error);
    }
  },
};
