const siteService = require("../../services/site/site.service");
module.exports = {
  friendlyName: "editArea",
  description: "",
  example: [`curl -X PUT "http://localhost:1337/site/v2/area/:areaId`],
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      description: "",
    },
    areaId: {
      type: "string",
      required: true,
    },
    name: {
      type: "string",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[site > editArea] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[site > editArea] Bad Request!",
    },
    notFound: {
      statusCode: 404,
      description: "[site >editArea] resource not found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { areaId, name, _userMeta } = inputs;
      let { _site: siteId } = _userMeta;
      let siteData = await siteService.findOne({ siteId });
      if (!siteData)
        return exits.notFound({ problems: [`siteId "${siteId}" not found`] });

      let { areas } = siteData;
      if (!areas) {
        areas = {};
      }

      if (!areas[areaId])
        return exits.notFound({
          problems: [`area "${areaId}" doesn't exist at siteId "${siteId}" `],
        });

      //List of all existing area name
      const areaNames = Object.keys(areas).map((it) => areas[it].name);

      if (areaNames.indexOf(name) !== -1)
        return exits.badRequest({
          problems: [
            `An area by name of ${name} already exists at siteId ${siteId}. Please enter new area`,
          ],
        });

      areas[areaId].name = name;
      await siteService.update(
        { siteId },
        {
          areas: JSON.stringify(areas),
        }
      );
      const DTO = {
        areaId:areaId,
        name:name
      }
      return exits.success(DTO);
    } catch (error) {
      sails.log.error("[site > editArea] Error!");
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
