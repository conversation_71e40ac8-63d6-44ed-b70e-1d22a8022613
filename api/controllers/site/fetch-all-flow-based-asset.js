const { getComponentListByDriverAndDeviceType } = require('../../services/component/component.public');
const { getComponentListByAbbr } = require('../../services/component/component.service.js');
const { getDeviceListByDriverAndDeviceType } = require('../../services/device/device.public');
const { getAllFlowBasedDevices } = require('../../services/device/device.service.js');
const { validateFetchAllFlowBasedAsset } = require('../../utils/site/requestValidator.util.js');

module.exports = {


  friendlyName: 'Fetch all flow based asset',


  description: '',


  inputs: {
    deviceDriverDetails: {
      type: 'json',
      required: true
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' }
    },
  },


  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[site > fetch-all-flow-based-asset] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[site > fetch-all-flow-based-asset] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[site > fetch-all-flow-based-asset] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[site > fetch-all-flow-based-asset] Not Found'
    },
    success: {
      statusCode: 200,
      description: '[site > fetch-all-flow-based-asset] Asset list fetched successfully'
    }
  },


  fn: async function (inputs, exits) {
    try {
      const { deviceDriverDetails, _userMeta: { _site: siteId } } = inputs;
      const { deviceDriverDetails: _deviceDriverDetails } = validateFetchAllFlowBasedAsset({ deviceDriverDetails });
      const componentsDeviceDriverDetails = _deviceDriverDetails.filter(item => item.class === 'component');
      const devicesDeviceDriverDetails = _deviceDriverDetails.filter(item => item.class === 'device');
      const batchAssetList = [];
      batchAssetList.push(getComponentListByAbbr(componentsDeviceDriverDetails, siteId), getAllFlowBasedDevices(devicesDeviceDriverDetails, siteId));
      const assetList = await Promise.all(batchAssetList);
      return exits.success(assetList.flat());
    } catch (error) {
      if (error.code == 'E_INPUT_VALIDATION') {
        return exits.badRequest({ error: error.message })
      }
      return exits.serverError(error)
    }
  }


};
