const self = require('../../services/sync/sync.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/sync/mode-change.util');

module.exports = {
  friendlyName: 'Mode-Change',
  description: 'When modes are changed it finds the relevant recipes and issues a play/pause command.',

  example: [

  ],

  inputs: {
    controllerId: {
      type: 'string',
      example: '1234'
    },
    siteId: {
      type: 'string',
      example: 'ssh',
      required: true
    },
    modes: {
      type: {},
      example: {'did.param': 'joulerecipe'},
      required: true
    },
    requestId : {
      type: 'string',
      example: 'SJAKS-60eff1fc-ada9-46dd-a598-e4c5264c2b4d-ifdi-1749644796901',
      required: true
    },
    traceId : {
      type: 'string',
      example: 'SJAKS-60eff1fc-ada9-46dd-a598-e4c5264c2b4d-ifdi-1749644796901',
      required: true
    },
    secret: {
      type: 'string',
      example: 'sha-256 code',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[sync > mode-change] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[sync > mode-change] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[sync > mode-change] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    let { siteId, modes, secret, requestId, traceId } = inputs;
    try {
      sails.log.info(
        `level="INFO" app="jt-api-v2" operation="recipePlayPauseOnModeChange" step="2/5" traceId="${traceId}" requestId="${requestId}" message="Request for Recipe Play/Pause after mode change is received on jt-api-v2" state="in-progress" siteId="${siteId}" modeFeedback="${JSON.stringify(modes)}"`,
      );
      if (secret !== "41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4")
        return exits.badRequest({ problems: ["Invalid secret passed"] });

      if (globalHelper.isNullish(modes)) return exits.success();

      let recipes = await self.getAllDeployedRecipesOfASite(siteId);

      if (recipes.length === 0) {
        sails.log.info(
          `[Mode-Change] siteId= ${siteId} requestId= ${requestId} modesPayload=${JSON.stringify(modes)} message="No deployed recipes found for the site"`,
        );
        return exits.success();
      }
      const recipeList = recipes.map((it) => it.rid);
      recipes = selfUtils.removePausedRecipes(recipes);
      if (recipes.length === 0) {
        sails.log.info(
          `[Mode-Change] siteId= ${siteId} requestId= ${requestId} modesPayload=${JSON.stringify(modes)} message="No Active recipes found for the site" recipes=${JSON.stringify(recipeList)}`,
        );
        return exits.success();
      }

      let controllerRecipeMap = selfUtils.getAffectedRecipesMap(recipes, modes);

      if (globalHelper.isNullish(controllerRecipeMap)) {
        sails.log.info(
          `[Mode-Change] siteId= ${siteId} requestId= ${requestId} modesPayload=${JSON.stringify(modes)} message="no controller recipe map found for the site" recipes=${JSON.stringify(recipeList)}`,
        );
        return exits.success();
      }

      controllerRecipeMap = selfUtils.getRecipePacketForNewModes(controllerRecipeMap, modes);

      if (controllerRecipeMap.server) {
        await self.handleServerRecipes(controllerRecipeMap.server, siteId);
        delete controllerRecipeMap.server;
      }

      if (globalHelper.isNullish(controllerRecipeMap)) {
        sails.log.info(
          `[Mode-Change] siteId= ${siteId} requestId= ${requestId} modesPayload=${JSON.stringify(modes)} message="No to recipe controller relationship exist" recipes=${JSON.stringify(recipeList)}`,
        );
        return exits.success();
      }

      await self.updateRecipeStateInSyncTable(controllerRecipeMap);

      await self.sendStartStopToControllers(siteId, controllerRecipeMap, requestId);
      sails.log.info(
        `[Mode-Change] siteId= ${siteId} requestId= ${requestId} modesPayload=${JSON.stringify(modes)} message="Successfully recipe sync" recipes=${JSON.stringify(recipeList)} controllerRecipeMap=${JSON.stringify(controllerRecipeMap)}`,
      );
      return exits.success();
    } catch (e) {
      sails.log.error(`[Mode-Change] error=${e}`);
      sails.log.error("Error in mode-change: " + e);
      return exits.serverError(e);
    }
  },
};
