const self = require('../../services/sync/sync.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/sync/sync-recipe.util');

module.exports = {

  friendlyName: 'Sync Recipes',
  description: 'This API returns the latest config of the requested recipes.',

  example: [

  ],

  inputs: {
    controllerId: {
      type: 'string',
      example: '1234',
      required: true
    },
    ridList: {
      type: ['ref'],
      example: ['abcd', 'efgh'],
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[sync > sync-recipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[sync > sync-recipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[sync > sync-recipe] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { controllerId, ridList } = inputs;
      ridList = ridList.filter(globalHelper.unique);
      let syncData = await self.find({ controllerId });
      let finalSyncDataPacket = selfUtils.filterAndFormSyncPacket(syncData, ridList);

      exits.success(finalSyncDataPacket);

    } catch (e) {
      sails.log.error('Error in sync-recipe: ' + e);
      return exits.serverError(e);
    }
  }
};
