const csv = require("csvtojson");
const {
  svgTaggingConfiguration,
} = require("../../services/configurationHierarchy/configurationHierarchy.service");
module.exports = {
  friendlyName: "get svg tagging",
  description: "get svg tagging",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
    },
    systemId: {
      type: "number",
      required: true,
    },
    leaf_node_id: {
      type: "number",
    },
    strict_hierarchy: {
      type: "number",
      isIn: [1, 0],
      required: true,
    },
    key: {
      type: "string",
    },
  },
  exits: {
    success: {
      statusCode: 200,
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server error",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
    sizeLimitExceeded: {
      statusCode: 413,
      description: "Size limit exceeded",
    },
    timeOut: {
      statusCode: 408,
      description: "Timeout",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {
        systemId,
        leaf_node_id,
        strict_hierarchy,
        key,
        _userMeta: { _site: siteId },
      } = inputs;

      if (strict_hierarchy == 1 && leaf_node_id === undefined) {
        return exits.badRequest({
          code: "E_LEAF_NODE_NOT_FOUND",
          message: `leaf_node_id input value is not found`,
          problems: ["leaf_node_id input value is not found"],
        });
      }
      if (strict_hierarchy == 0 && key === undefined) {
        return exits.badRequest({
          code: "E_KEY_NOT_FOUND",
          message: `key input not found.`,
          problems: ["key input not found."],
        });
      }

      if (strict_hierarchy == 1) {
        let response = await svgTaggingConfiguration.getSVGTaggingMapByLeafNode(
          leaf_node_id,
          systemId
        );
        if (!response) {
          return exits.badRequest({
            code: "E_TAGGING_NOT_FOUND",
            message: `svg tagging not exist first upload the csv`,
            problems: ["svg tagging not exist first upload the csv"],
          });
        }
        return exits.success(response);
      }

      let response = await svgTaggingConfiguration.getSVGTaggingMapByKey(
        siteId,
        systemId,
        key
      );
      if (!response) {
        return exits.badRequest({
          code: "E_TAGGING_NOT_FOUND",
          message: `svg tagging not exist first upload the csv`,
          problems: ["svg tagging not exist first upload the csv"],
        });
      }
      return exits.success(response);
    } catch (e) {
      sails.log.info(`Error: ${e.message}`);
      return exits.serverError(e.message);
    }
  },
};
