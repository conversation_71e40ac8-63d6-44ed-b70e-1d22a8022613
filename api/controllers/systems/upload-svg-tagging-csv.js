const csv = require("csvtojson");
const fs = require("fs");
const util = require("../../utils/systems/upload-svg-tagging-csv.util");
const {
  svgTaggingConfiguration,
} = require("../../services/configurationHierarchy/configurationHierarchy.service");
module.exports = {
  friendlyName: "upload svg taging csv file",
  description: "upload svg tagging csv file",
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
    },
    systemId: {
      type: "number",
      required: true,
    },
    leaf_node_id: {
      type: "number",
    },
    strict_hierarchy: {
      type: "number",
      isIn:[1,0],
      required: true,
    },
    key: {
      type: "string",
    },
    svg_tagging_map: {
      type: "string",
      required: true,
      description: "its a base64 encoded array of svg location id.",
    },
  },
  exits: {
    success: {
      statusCode: 200,
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      responseType: "serverError",
      description: "Server error",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
    sizeLimitExceeded: {
      statusCode: 413,
      description: "Size limit exceeded",
    },
    timeOut: {
      statusCode: 408,
      description: "Timeout",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {
        systemId,
        leaf_node_id,
        strict_hierarchy,
        key,
        svg_tagging_map,
        _userMeta: { _site: siteId },
      } = inputs;

      if (strict_hierarchy == 1 && leaf_node_id === undefined) {
        return exits.badRequest({
          code: "E_LEAF_NODE_NOT_FOUND",
          message: `leaf_node_id input value is not found`,
          problems: ["leaf_node_id input value is not found"],
        });
      }
      if (strict_hierarchy == 0 && key === undefined) {
        return exits.badRequest({
          code: "E_KEY_NOT_FOUND",
          message: `key input not found.`,
          problems: ["key input not found."],
        });
      }


      const svgTaggingSet = util.base64ToSvgTaggingSet(svg_tagging_map);
      if (!svgTaggingSet) {
        return exits.badRequest({
          code: "E_INVALID_SVG_TAGGING_SET",
          message: `svg_tagging_map value is not correct`,
          problems: ["svg_tagging_map value is not correct"],
        });
      }
      this.req.file("file").upload({}, uploadHandler);
      async function uploadHandler(err, uploadedFiles) {
        if (err && err.code === "E_EXCEEDS_UPLOAD_LIMIT") {
          return exits.badRequest({
            code: "E_EXCEEDS_UPLOAD_LIMIT",
            message: `File size limit exceeded`,
            problems: ["File size limit exceeded"],
          });
        }
        if (err) {
          return exits.serverError(err);
        }

        if (_.isEmpty(uploadedFiles)) {
          return exits.badRequest({
            code: "E_FILE_NOT_FOUND",
            message: `svg tagging file missing`,
            problems: ["svg tagging file missing"],
          });
        }

        let _tempUploadedFilePath = uploadedFiles[0].fd;
        let jsonData = await csv().fromFile(_tempUploadedFilePath);
        fs.unlinkSync(_tempUploadedFilePath);

        if (!util.isValidCSV(jsonData)) {
          return exits.badRequest({
            code: "E_INVALID_CSV_FORMAT",
            message: `csv file should have svg_loc_id,type and device_id columns`,
            problems: [
              `csv file should have svg_loc_id,type and device_id columns`,
            ],
          });
        }


        let isValidSVGTagging = util.validateSVGTaggingWithCSV(
          new Set(jsonData.map((it) => it.svg_loc_id)),
          svgTaggingSet
        );
        if (!isValidSVGTagging) {
          return exits.badRequest({
            code: "E_MANIPULATED_CSV_FILE",
            message: `CSV file data is not matching with SVG tagging.Please do not add/remove/update any item from column "svg_loc_id"`,
            problems: [
              `CSV file data is not matching with SVG tagging.Please do not add/remove/update any item from column "svg_loc_id`,
            ],
          });
        }
        jsonData = jsonData.sort((a, b) => {
          return a.svg_loc_id > b.svg_loc_id ? 1 : -1;
        });
        if (strict_hierarchy == 1) {
          let response = await svgTaggingConfiguration.saveSVGTagingByLeafNode(
            systemId,
            leaf_node_id,
            jsonData
          );
          if (response) {
            return exits.badRequest({
              ...response,
              problems: [response.message],
            });
          }
          return exits.success({});
        }

        let response = await svgTaggingConfiguration.saveTaggingMapBykey(
          siteId,
          systemId,
          key,
          jsonData
        );
        if (response) {
          return exits.badRequest({
            ...response,
            problems: [response.message],
          });
        }
        return exits.success({});
      }
    } catch (e) {
      sails.log.info(`Error: ${e.message}`);
      return exits.serverError(e.message);
    }
  },
};
