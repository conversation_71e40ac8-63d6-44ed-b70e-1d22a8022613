
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');

module.exports = {
  friendlyName: 'createSystem',
  description : 'Used to initialize a systemId and a system in the systems table',
  example: [
    `curl --location --request POST 'http://localhost:1338/m2/configuration/v1/system' \
    --header 'Authorization: Bearer ' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "Fire Fighting",
        "componentList": [
            "fireAlarm"
        ]
    }'`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    name: {
      type: 'string',
      required: true,
      example: "HVAC",
      description: 'Name of the system',
    },
    componentList: {
      type: 'json',
      required: true,
      // example: ["chiller", "ahu"],
      description: 'Array list of chiller types supported by the system',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[systems > createSystem] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[systems > createSystem] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[systems > createSystem] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const { name, componentList } = inputs;
      // TODO:
      // Input checks to see if the componentTypes exist or not.
      // Not checking for componentType currently, as not implemented by FE to limit only these componentTypes while adding a component.
      await configurationHierarchyService.systems.create({
        name,
        component_list: componentList
      });
      return exits.success({ 'status': true });
    } catch(error) {
      sails.log.error('[systems > createSystem] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
