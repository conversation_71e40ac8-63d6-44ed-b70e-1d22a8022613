
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');
const selfUtils = require('../../utils/systems/fetch-system-details.util.js');

module.exports = {
  friendlyName: 'fetchSystemDetails',
  description : 'Used to fetch all hierarchy data from system_nodes and all configured nodes (layers, controllers and components) from DB. \
  Response format is in a hierarchical format that FE requires.',
  example: [
    `curl --location --request POST 'http://localhost:1338/m2/configuration/v1/site/:siteId/system/1' \
    --header 'Authorization: Bearer ' \
    --data-raw ''`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      // required: true,
      example: 'mgch',
      description: 'siteId of the site.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[systems > fetchSystemDetails] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[systems > fetchSystemDetails] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[systems > fetchSystemDetails] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        _userMeta: { _site: tokenSiteId }, siteId: queryParamSiteId
      } = inputs;
      let siteId = queryParamSiteId ? queryParamSiteId : tokenSiteId;
      // TODO: Figure out how to use 'where in' clause of postgres instead of querying multiple times.
      let enabledSites = await configurationHierarchyService.siteSystemMapping.find({
        site_id: siteId
      });
      let systemIdList = enabledSites.map(siteSystemMappingInfo => siteSystemMappingInfo.system_id );
      let $enabledSystems = systemIdList.map(systemId => {
        return configurationHierarchyService.systems.findOne({ id: systemId });
      });
      let $systemNodes = systemIdList.map(systemId => {
        return configurationHierarchyService.systemNodes.find({ system_id: systemId });
      });

      let enabledSystemsResponse = await Promise.all($enabledSystems);
      let enabledSystems = enabledSystemsResponse.filter(Boolean);
      let systemNodesResponse = await Promise.all($systemNodes);
      let systemNodes = []
      systemNodesResponse.forEach(systemNodesPerSystem => {
        systemNodesPerSystem.forEach(systemNode =>{
          if(systemNode) systemNodes.push(systemNode)
        });
      });
      let configuredNodes = await configurationHierarchyService.nodes.find({
        site_id: siteId,
        is_deleted: false
      });

      const { systemNodeIdMap, systemNodeChildrenMap, systemRootNode } = selfUtils.generateSystemNodesMapObjects(systemNodes);
      let hierarchyData = enabledSystems.map(system => {
        let rootNodeId = systemRootNode[system.id], layers = {};
        if (rootNodeId) // Incase no layers have been configured on the system yet.
          layers = selfUtils.generateHierarchyLayerData(rootNodeId, systemNodeChildrenMap, systemNodeIdMap, true);
        return {
          systemId: system.id,
          systemName: system.name,
          layers
        };
      });

      const { nodeIdConfigMap, nodeConfigChildrenMap, rootNodeConfigMap } = selfUtils.generateNodesMapsObjects(configuredNodes);
      let layerData = enabledSystems.map(system => {
        let { name: systemName, id: systemId } = system, layerConfig = [];
        let rootNodeIds = rootNodeConfigMap[systemId];
        if (rootNodeIds) { // In case no layers/components/controllers have been configured on the system yet.
          layerConfig = rootNodeIds.map(rootNodeId => {
            return selfUtils.generateHierarchyLayerData(rootNodeId, nodeConfigChildrenMap, nodeIdConfigMap, false);
          });
        }
        return {
          systemName,
          systemId,
          layerConfig
        };
      });

      let returnObject = {
        hierarchyData,
        layerData
      }

      return exits.success(returnObject);
    } catch(error) {
      sails.log.error('[systems > fetchSystemDetails] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
