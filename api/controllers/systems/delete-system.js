
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');

module.exports = {
  friendlyName: 'deleteSystem',
  description : 'Only to be used from BE for clearing out a system. Not to be integrated with FE. Route will also be disabled. Deletes everything \
  related to the system in systems, sitesystemmapping, system_nodes and nodes table.',
  example: [
    `curl --location --request DELETE 'http://localhost:1338/m2/configuration/v1/system' \
    --header 'Authorization: Bearer ' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "systemId": 3
    }'`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    systemId: {
      type: 'number',
      required: true,
      example: 17,
      description: 'System ID which needs to be deleted.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[systems > deleteSystem] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[systems > deleteSystem] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[systems > deleteSystem] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const { systemId } = inputs;

      let inputCheckOutput = selfUtils.checkInput(inputs);
      if(!inputCheckOutput.status)
        return exits.badRequest({
          "status": false,
          problems: inputCheckOutput.errors
        });
      await configurationHierarchyService.siteSystemMapping.delete({ system_id: systemId });
      await configurationHierarchyService.systems.delete({ id: systemId });
      await configurationHierarchyService.systemNodes.delete({ system_id: systemId });
      await configurationHierarchyService.nodes.delete({ system_id: systemId });

      return exits.success({
        "status": true
      });
    } catch(error) {
      sails.log.error('[systems > deleteSystem] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
