const smartAlertService = require("../../services/smartAlert/smartAlert.public");
module.exports = {
  friendlyName: "syncRecipeInSmartAlerts",
  description: "Sync recipes in smart alerts for a particular site",
  example: [`curl -X POST "http://localhost:1337/syncRecipeInSmartAlerts`],

  inputs: {
    rid: {
      type: "string",
      example: "039858ae-40fb-4155-96da-f916f434ed79",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[sync-recipe-in-smart-alerts] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[sync-recipe-in-smart-alerts] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[sync-recipe-in-smart-alerts] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { rid } = inputs;
      const response = await smartAlertService.syncRecipeInSmartAlert(rid);
      return exits.success(response);
    } catch (error) {
      sails.log.error("[sync-recipe-in-smart-alerts] Error!");
      sails.log.error(error);
      switch (error.code) {
        case "VALIDATION_ERROR":
          return exits.badRequest({
            message: error.message,
            code: error.code,
          });
        case "RECIPE_ALERT_ERROR":
          return exits.badRequest({
            message: error.message,
            code: error.code,
          });
        case "ALERT_RECIPE_ID_NOT_FOUND":
          return exits.notFound({
            message: error.message,
            code: error.code,
          });
        case "SYNC_ERROR":
          return exits.badRequest({
            message: error.message,
            code: error.code,
          });
        case "DATABASE_ERROR":
          return exits.serverError({
            message: error.message,
            code: error.code,
          });
        default:
          return exits.serverError(error);
      }
    }
  },
};
