const smartAlertService = require("../../services/smartAlert/smartAlert.public");

module.exports = {
  friendlyName: "syncRecipeInSmartAlertBySiteId",
  description: "Sync recipes in smart alerts for a particular site by siteId",
  example: [`curl -X POST "http://localhost:1337/syncRecipeInSmartAlertBySiteId`],

  inputs: {
    siteId: {
      type: "string",
      example: "iah-del",
      required: true,
      description: "The ID of the site to sync recipes for",
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[sync-recipe-in-smart-alert-by-siteId] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[sync-recipe-in-smart-alert-by-siteId] Bad Request!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[sync-recipe-in-smart-alert-by-siteId] Forbidden Request!",
    },
    notFound: {
      statusCode: 404,
      description: "Not Found",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId } = inputs;
      const response = await smartAlertService.syncRecipeInSmartAlertBySiteId(siteId);
      return exits.success(response);
    } catch (error) {
      sails.log.error("[sync-recipe-in-smart-alert-by-siteId] Error!");
      sails.log.error(error);
      switch (error.code) {
        case "VALIDATION_ERROR":
          return exits.badRequest({
            message: error.message,
            code: error.code,
          });
        case "RECIPE_ALERT_ERROR":
          return exits.badRequest({
            message: error.message,
            code: error.code,
          });
        case "ALERT_SITE_ID_NOT_FOUND":
          return exits.notFound({
            message: error.message,
            code: error.code,
          });
        case "SYNC_ERROR":
          return exits.badRequest({
            message: error.message,
            code: error.code,
          });
        case "DATABASE_ERROR":
          return exits.serverError({
            message: error.message,
            code: error.code,
          });
        default:
          return exits.serverError(error);
      }
    }
  },
};
