const userService = require('../../services/user/user.service');
const uuid = require('uuid');
const notifySlack = require('../../services/notification/slack/notify.public');
module.exports = {
  friendlyName: 'deleteUser',
  description : 'Changes the password and adds the tag "isDeleted" to user entry.',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    userList: {
      type: ['ref'],
      required: true,
      example: ["<EMAIL>"],
      description: 'List of User Ids of the users that need to be deleted.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > deleteUser] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > deleteUser] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > deleteUser] forbidden Request!',
    },
    success: {
      statusCode: 200,
      description: '[user > deleteUser] Successfully deleted'
    }
  },

  fn: async function (inputs, exits) {
    try{
      const { userList } = inputs;
      let $userList = [];
      userList.forEach(userId => $userList.push(userService.findOne({userId, isDeleted: false})));
      let newPassword = uuid.v4();
      let userObjects = await Promise.all($userList);
      userObjects = userObjects.filter(Boolean);
      const usersToDelete  = [];
      const responseSchema =  {
        msg: "User does not exist"
      }
      for (userObject of userObjects) {
        delete userObject.createdAt;
        delete userObject.updatedAt;
        Object.assign(userObject, {
          "isDeleted": true,
          "password": newPassword
        })
        usersToDelete.push({
          name: userObject.name,
          email: userObject.email
        })
      };
      let promiseList = [];
      userObjects.forEach(userObject => promiseList.push(userService.create(userObject)));
      await Promise.all(promiseList);
      await userService.removeUsersOnNovu(userList)
      const requesterInfo = {
        name: inputs._userMeta.name,
        email: inputs._userMeta.id
      }
      if (!_.isEmpty(usersToDelete)) {
        responseSchema.msg = 'Successfully deleted';
        await notifySlack.notifySlackOnDelete(usersToDelete, requesterInfo)
      }
      return exits.success(responseSchema);
    } catch(error) {
      sails.log.error('[user > deleteUser] Error!');
      sails.log.error(error);
      return exits.serverError(error);
    }
  }
};
