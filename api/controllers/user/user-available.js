const self = require('../../services/user/user.service');

module.exports = {
  friendlyName: 'User-Available',
  description : 'It checks if the user ID is available or not',
  example: [
    `curl -X POST "http://localhost:1337/m2/user/v2/user/available`,
  ],

  inputs: {
    userId: {
      type: 'string',
      required: true,
      example: '<EMAIL>'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > userAvailable] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > userAvailable] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > userAvailable] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > userAvailable] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { userId } = inputs;
      let user = await self.findOne({ userId });

      if (user) return exits.forbidden({ problems: ['User with this ID exists'] });
      return exits.success();
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
