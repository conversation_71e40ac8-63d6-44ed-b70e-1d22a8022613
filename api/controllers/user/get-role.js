const defaultPolicies = require('../../../config/policy.json');
const roleService = require('../../services/role/role.public');
const deepmerge = require('deepmerge');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/user/utils');

module.exports = {
  friendlyName: 'Get-Role',
  description : 'It gets all roles',
  example: [
    'curl -X GET "http://localhost:1337/m2/user/v2/role',
    'curl -X GET "http://localhost:1337/m2/user/v2/role?roleName=admin'
  ],

  inputs: {
    roleName: {
      type: 'string',
      example: 'admin',
      description: 'Name of the role'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > getRole] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > getRole] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > getRole] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > getRole] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { roleName } = inputs;
      let query = {};
      if (roleName) query['roleName'] = roleName;

      let allRoles = await roleService.find(query);
      if (globalHelper.isNullish(allRoles)) return exits.forbidden({ problems: ['Role not found!'] });

      if (roleName) {
        // Take the diff of role with policies and then return the role info
        allRoles = allRoles[0];
        allRoles.policies = deepmerge(defaultPolicies, globalHelper.toJson(allRoles.policies)) || {};
        allRoles.defpref = globalHelper.toJson(allRoles.defpref) || {};
        utils.addDefaultPreferences(allRoles.defpref);
        return exits.success(allRoles);
      } else {
        // Return an array of all the existing roleNames
        let roleNames = allRoles.map((eachRole) => {
          let roleObject = { roleName: eachRole.roleName };
          return roleObject;
        });
        return exits.success(roleNames);
      }
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
