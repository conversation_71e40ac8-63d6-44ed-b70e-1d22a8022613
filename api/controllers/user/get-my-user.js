
const userService = require('../../services/user/user.service');

module.exports = {
  friendlyName: 'Get My User',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/m2/user/v2/user`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {'id' : 'userId', '_role' : 'role'}
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > get-my-user] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > get-my-user] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > get-my-user] unauthorized!',
    },
  },

  fn: async function (inputs, exits) {
    let { _userMeta } = inputs;
    let userId = _userMeta.id;
    try{

      let user = await userService.findOne({ userId });
      if(!user) return exits.badRequest({'problems' : ['Not a valid user']});
      return exits.success(user);

    } catch(error) {
      sails.log.error('[user > get-my-user] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
