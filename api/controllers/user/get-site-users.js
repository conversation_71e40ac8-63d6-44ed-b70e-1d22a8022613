const flaverr = require('flaverr');
const userService = require('../../services/user/user.private');

module.exports = {
  friendlyName: 'Get All Users for a Specific Site',
  description: 'Fetch users to a specific site.',

  inputs: {
    _userMeta: {
      type: 'json',
      example: { id: 'userId', _role: 'role' },
    },
    siteId: {
      type: 'string',
      description: 'The ID of the site to fetch users for',
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > get-site-users] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > get-site-users] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > get-site-users] unauthorized!',
    },
  },

  fn: async function ({ siteId }, exits) {
    try {
      const users = await userService.fetchUsersBySiteId(siteId);
      return exits.success(users);
    } catch (error) {
      sails.log.error('[user > get-site-users] Error:', error);
      if (flaverr.taste('E_INVALID_ARGUMENT', error)) {
        return exits.badRequest({ problems: ['Site ID is required'] });
      }
      return exits.serverError({ message: 'Server Error while fetching users', error });
    }
  },
};
