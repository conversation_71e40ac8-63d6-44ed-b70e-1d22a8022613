
const userService = require('../../services/user/user.service');

module.exports = {
  friendlyName: 'getComponentCardConfig',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: false // this fields auto add authentication
    },
    id: {
      type: 'string',
      description: 'Unique Component OR Process ID.',
      example: 'mgch_1',
      required: false
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > getComponentCardConfig] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > getComponentCardConfig] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > getComponentCardConfig] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { _userMeta, id } = inputs;
      const userId = _userMeta.id;
      const siteId = _userMeta._site;
      const params = {
        'userId_siteId': `${userId}_${siteId}`,
        id
      };
      let queryResponse;
      try {
        queryResponse = await userService.UserComponentCards.find(params);
      } catch (error) {
        sails.log.error('[user > getComponentCardConfig] Error fetching component cards configuration!');
        sails.log.error(error);
        return exits.serverError(error);
      }

      if (!queryResponse || queryResponse.length === 0){
        return exits.success([]);
      }
      const response = queryResponse[0].config;
      return exits.success(response);

    } catch(error) {
      sails.log.error('[user > getComponentCardConfig] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
