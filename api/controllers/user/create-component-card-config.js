
const userService = require('../../services/user/user.service');

module.exports = {
  friendlyName: 'createComponentCardConfig',
  description : '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true // this fields auto add authentication
    },
    id: {
      type: 'string',
      description: 'Unique Component OR Process ID.',
      example: 'mgch_1',
      required: true
    },
    config: {
      type: ['string'],
      description: 'Array of abbs in the order the cards need to be displayed.',
      example: ['tmp','hum'],
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > createComponentCardConfig] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > createComponentCardConfig] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > createComponentCardConfig] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { id, config, _userMeta } = inputs;
      const userId = _userMeta.id;
      const siteId = _userMeta._site;

      let params = {
        'userId_siteId': `${userId}_${siteId}`,
        'siteId': siteId,
        id,
        config
      };
      try {
        await userService.UserComponentCards.create(params);
      } catch (error) {
        sails.log.error('Error creating entry in userComponentCards table while saving configuration');
        sails.log.error(error);
        return exits.serverError(error);
      }

      return exits.success({
        status: true,
        message: 'Configuration successfuly stored.'
      });
    } catch(error) {
      sails.log.error('[user > createComponentCardConfig] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
