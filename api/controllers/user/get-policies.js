const defaultPolicies = require('../../../config/policy.json');
const userUtils = require('../../utils/user/utils');

module.exports = {
  friendlyName: 'Get-Policies',
  description : 'It gets all the listed policies',
  example: [
    `curl -X GET "http://localhost:1337/m2/user/v2/policies`,
  ],

  inputs: {

  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > getPolicies] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > getPolicies] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > getPolicies] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > getPolicies] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      if (!userUtils.validatePolicies(defaultPolicies)) return exits.forbidden({problems: ['Invalid policy structure in backend.']});
      return exits.success(defaultPolicies);
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
