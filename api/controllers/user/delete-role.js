const userUtils = require('../../utils/user/utils');
const roleService = require('../../services/role/role.public');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.public');
const globalHelper = require('../../utils/globalhelper');
const userService = require('../../services/user/user.public');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.public');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'Delete-Role',
  description: 'It deletes a specific role',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    roleName: {
      type: 'string',
      required: true,
      example: 'admin'
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: '<EMAIL>',
        _role: 'role',
        _site: 'siteId'
      },
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > Delete Role] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > Delete Role] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > Delete Role] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > Delete Role] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {
        roleName,
        _userMeta: {
          id: user_Id,
          _site: site_Id
        }
      } = inputs;

      const roleInfo = await roleService.findOne({ roleName });
      if (!roleInfo) return exits.badRequest({ problems: ['This role does not exist'] });
      if (roleInfo.isDeleted === '1') return exits.success();

      let affectedUsers = await userSiteMapService.find({ role: roleName });

      let affectedUsersSiteMap = {};
      let $updateUserPromiseArray = affectedUsers.map((eachUser) => {
        let {
          userId,
          siteId
        } = eachUser;
        if (userId in affectedUsersSiteMap) {
          affectedUsersSiteMap[userId].push(siteId);
        } else {
          affectedUsersSiteMap[userId] = [siteId];
        }
        return userSiteMapService.update({
          userId,
          siteId
        }, { role: userUtils.getDefaultRole() });
      });

      /***Updating the policiesGroup key of the affected users below. */
      let $updatePoliciesGroupPromiseArray = [];

      for (let userId in affectedUsersSiteMap) {
        let user = await userService.findOne({ userId });
        let {
          policiesGroup,
          userOrganization
        } = user;
        policiesGroup = globalHelper.toJson(policiesGroup);
        for (let siteId of affectedUsersSiteMap[userId]) {
          policiesGroup[siteId] = userUtils.getDefaultRole();
        }
        $updatePoliciesGroupPromiseArray.push(userService.update({
          userId,
          userOrganization
        }, { policiesGroup: JSON.stringify(policiesGroup) }));
      }

      await roleService.delete({ roleName });
      if ($updateUserPromiseArray.length > 0) await Promise.all($updateUserPromiseArray);
      if ($updatePoliciesGroupPromiseArray.length > 0) await Promise.all($updatePoliciesGroupPromiseArray);

      const auditPayload = {
        event_name: 'state_delete_role',
        user_id: user_Id,
        site_id: site_Id,
        asset_id: 'Role-' + roleName,
        req: this.req,
        prev_state: roleInfo,
        curr_state: null,
      };

      auditEventLogService.emit(auditPayload);

      await notifyJouleTrackPublicRoom(
        site_Id, 'userRoles', {
          event: 'delete',
          data: { roleName }
        }
      );

      return exits.success({ status: 'User Role Deleted Successfully' });
    } catch (e) {
      sails.log.error('[user > Delete Role]', e);
      return exits.serverError(e);
    }
  }
};
