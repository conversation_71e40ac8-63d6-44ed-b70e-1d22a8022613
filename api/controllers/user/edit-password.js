const self = require('../../services/user/user.service');

module.exports = {
  friendlyName: 'Edit-Password',
  description: 'It edits the password of the user',
  example: [
    `curl -X POST "http://localhost:1337/m2/user/v2/user/editPassword`,
  ],

  inputs: {
    oldPassword: {
      type: 'string',
      required: true,
      example: 'password'
    },
    newPassword: {
      type: 'string',
      required: true,
      example: 'password'
    },
    userOrganization: {
      type: 'string',
      required: true,
      example: 'password'
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: '<EMAIL>'
      }
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > edit-password] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > edit-password] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > edit-password] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > edit-password] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let userId = inputs._userMeta['id'];
      if (!userId) return exits.badRequest({ problems: ['User ID is not present'] });

      let { oldPassword, newPassword, userOrganization } = inputs;
      let user = await Users.findOne({ userId, userOrganization });
      if (!user) return exits.forbidden({ problems: ['User not found'] });

      const decryptedOldPassword = self.decryptPassword(oldPassword);
      let isValid = await self.comparePassword(decryptedOldPassword, user.password);
      if (!isValid) return exits.badRequest({ problems: ['Old password did not match'] });

      const passwordUpdateTime = Date.now();
      const decryptedNewPassword = self.decryptPassword(newPassword);
      await self.update({ userId, userOrganization }, { password: decryptedNewPassword, passwordUpdatedAt: passwordUpdateTime });

      exits.success({ message: 'Password Updated Successfully.' });
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
