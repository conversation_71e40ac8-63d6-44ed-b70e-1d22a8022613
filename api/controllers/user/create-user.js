const globalHelper = require('../../utils/globalhelper');
const createUserUtils = require('../../utils/user/create-user.util');
const userUtils = require('../../utils/user/utils');
const self = require('../../services/user/user.service');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.public');
const jwtService = require('../../services/auth/auth.public');
const notifySlack = require('../../services/notification/slack/notify.public');

module.exports = {
  friendlyName: 'Create-User',
  description : 'It creates a new user on Joule Track',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    email: {
      type: 'string',
      required: true,
      example: '<EMAIL>'
    },
    name: {
      type: 'string',
      required: true,
      example: 'Priya<PERSON>'
    },
    designation: {
      type: 'string',
      required: true,
      example: 'Software Development Engineer'
    },
    phone: {
      type: 'string',
      required: true,
      example: '9999888822'
    },
    userId: {
      type: 'string',
      example: '<EMAIL>'
    },
    password: {
      type: 'string',
    },
    userOrganization: {
      type: 'string',
      required: true,
      example: 'Smart Joules'
    },
    policiesGroup: {
      type: {},
      required: true,
      example: {
        'sjo': 'admin',
        'ssh': 'admin'
      }
    },
    defaultSite: {
      type: 'string',
      example: 'ssh'
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > createUser] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > createUser] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > createUser] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > createUser] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let userInfoObject = createUserUtils.createInitialUserInfoObject(inputs);

      if (userInfoObject.problems) return exits.badRequest(userInfoObject);

      let defaultRole = userInfoObject.policiesGroup[userInfoObject.defaultSite];
      let allRolesInfo = await self.getRolesInfo(userInfoObject.policiesGroup);

      if (allRolesInfo.problems) return exits.badRequest(allRolesInfo);

      let { userId, userOrganization, defaultSite } = userInfoObject;
      let user = await self.findOne({ userId });

      if (user) return exits.badRequest({ problems: [`The user (${userId}) already exists in the organization ${userOrganization}. Please choose a different userId.`] });

      userInfoObject = globalHelper.stringifyEachKeyOfObject(userInfoObject);
      await self.create(userInfoObject);

      if (!userInfoObject) return exits.badRequest({ problems: ['User not created'] });
      let policiesGroup = globalHelper.toJson(userInfoObject.policiesGroup);
      let $userSiteMapPromiseArray = [];
      try {
        for (let siteId in policiesGroup) {
          let role = policiesGroup[siteId];
          let phone = userInfoObject.phone;
          let allPreferences = globalHelper.toJson(allRolesInfo[role].defpref);
          userUtils.addDefaultPreferences(allPreferences);
          let userSiteMapObject = {
            userId,
            siteId,
            phone,
            role,
            ...allPreferences
          };
          let stringifiedUserObject = globalHelper.stringifyEachKeyOfObject(userSiteMapObject);
          $userSiteMapPromiseArray.push(userSiteMapService.create(stringifiedUserObject));
        }
        await Promise.all($userSiteMapPromiseArray);
      } catch (e) {
        await self.delete({userId});
        throw e;
      }

      const requesterInfo = {
        name: inputs._userMeta.name,
        email: inputs._userMeta.id,
      };
      userInfoObject.defaultRole = defaultRole;
      const {_id: novoSubscriberId} = await self.registerUserOnNovu(userInfoObject);
      userInfoObject.subscriber_id = novoSubscriberId;
      await self.update({userId, userOrganization}, {subscriber_id: novoSubscriberId});
      await notifySlack._notifySlackCreateUser(userInfoObject, requesterInfo);
      return exits.success({
        user: globalHelper.toJson(userInfoObject),
        token: jwtService.issueJWTToken({id: userId, firsTime: true, _role: defaultRole, _site: defaultSite}),
        siteId: userOrganization
      });
    } catch (error) {
      sails.log.error('user > createUser');
      sails.log.error(error);
      switch (error.code) {
        case 'E_USER_ALREADY_EXISTS':
          return exits.badRequest({ message: error.message });
        case 'E_UNAUTHORIZED_USER':
          return exits.unauthorized({message: 'User is unauthorized to create user'});
        default:{
          return exits.serverError(error);
        }
      }

    }
  }
};
