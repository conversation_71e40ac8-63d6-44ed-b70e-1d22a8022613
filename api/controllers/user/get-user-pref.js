const userUtils = require('../../utils/user/utils');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.public');
const globalhelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'Get-User-Preferences',
  description : 'It gets all the preferences of a user for a particular site.',
  example: [
    `curl -X POST "http://localhost:1337/m2/user/v2/userPref`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName', '_site': 'ssh' },
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > getUserPreferences] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > getUserPreferences] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > getUserPreferences] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > getUserPreferences] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let userInfo = inputs._userMeta;
      let { id: userId, _site: siteId } = userInfo;
      if (!userId || !siteId) return exits.badRequest({ problems: ['Invalid request parameters.'] });

      let userSiteInfo = await userSiteMapService.findOne({ userId, siteId });
      if (userSiteInfo === undefined) return exits.forbidden({ problems: ['User Info for this site not found.'] });

      let preferences = {
        dj: globalhelper.toJson(userSiteInfo.dj),
        djNotif: globalhelper.toJson(userSiteInfo.djNotif),
        mailConfig: globalhelper.toJson(userSiteInfo.mailConfig),
        msgConfig: globalhelper.toJson(userSiteInfo.msgConfig),
        unitPreference: globalhelper.toJson(userSiteInfo.unitPreference),
      };

      userUtils.addDefaultPreferences(preferences);
      return exits.success(preferences);
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
