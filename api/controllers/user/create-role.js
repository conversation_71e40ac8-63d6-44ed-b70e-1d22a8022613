const globalHelper = require('../../utils/globalhelper');
const userUtils = require('../../utils/user/utils');
const roleService = require('../../services/role/role.public');
const defaultPolicies = require('../../../config/policy.json');

module.exports = {
  friendlyName: 'Create-Role',
  description : 'It creates a new role',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    roleName: {
      type: 'string',
      required: true,
      example: 'admin'
    },
    pref: {
      type: 'json',
      required: true,
      example: {
        mailConfig: {
          JouleRecipe: {
            recipecomfortoptimization: 60
          }
        }
      }
    },
    policies: {
      type: 'json',
      required: true,
      example: {
        ACPlant: {
          'displayName': 'AC PLANT',
          'pageView': false,
          'subHeadingsList': [
            {
              'displayName': 'Command',
              'policyList': [
                {
                  'displayName': 'READ',
                  'routePolicyMap': {},
                  'hasAccess': false
                }
              ]
            }
          ]
        }
      }
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > createUser] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > createUser] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > createUser] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > createUser] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { roleName, pref, policies: policiesFromFE } = inputs;
      let policiesBE, defaultPoliciesBE;

      userUtils.addDefaultPreferences(pref);

      let role = await roleService.findOne({roleName});
      if (role) return exits.badRequest({ problems: ['This role already exists.'] });

      defaultPoliciesBE = userUtils.flattenPolicies(defaultPolicies);
      policiesBE = userUtils.flattenPolicies(policiesFromFE);

      for (let eachPolicy in defaultPoliciesBE) {
        if (policiesBE[eachPolicy]) defaultPoliciesBE[eachPolicy] = policiesBE[eachPolicy];
      }

      let roleInfoObject = {
        roleName,
        policies: policiesFromFE,
        policiesBE: defaultPoliciesBE,
        defpref: pref,
        isDeleted: '0'
      };
      let stringifiedObject = globalHelper.stringifyEachKeyOfObject(roleInfoObject);

      await roleService.create(stringifiedObject);
      return exits.success(stringifiedObject);

    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
