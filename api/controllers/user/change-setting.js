const notificationService = require("../../services/notification/slack/notify.public");
const userService = require("../../services/user/user.service");
const userSiteMapPublic = require("../../services/userSiteMap/userSiteMap.public");

module.exports = {
  friendlyName: "Change-Settings",
  description: "It updates the role of users for a site.",
  example: [
    `curl -X POST "http://localhost:1337/m2/user/v2/user/changeSetting`,
  ],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
    },
    userOperationData: {
      type: ["ref"],
      required: true,
      example: [
        {
          userId: "<EMAIL>",
          siteId: "ssh",
          role: "admin",
        },
      ],
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[user > changeSetting] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[user > changeSetting] Bad Request!",
    },
    unauthorized: {
      responseType: "unauthorized",
      description: "[user > changeSetting] unauthorized!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[user > changeSetting] forbidden Request!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const ResponseObject = {
        SUCCESS: [],
        FAILED: [],
        EXCEPTION: [],
        WARNING: [],
      };
      const {
        _userMeta: { name: requesterName, id: requesterUserId },
        userOperationData,
      } = inputs;
      const requesterInfo = {
        name: requesterName,
        email: requesterUserId
      }
      const allowedOperation = new Set([
        "userRoleUpdated",
        "siteAccessRemoved",
        "newSiteAssigned",
      ]);
      let batchOperationMap = userOperationData.reduce((acm, curr) => {
        const { operation } = curr;
        if (!allowedOperation.has(operation)) {
          ResponseObject["FAILED"].push({
            operation,
            data: curr,
            message: "operation now allowed",
          });
        } else {
          if (acm.hasOwnProperty(operation)) {
            acm[operation].push(curr);
          } else {
            acm[operation] = [curr];
          }
        }
        return acm;
      }, {});

      const batchOperationPromiseHolder = [];
      for (let operation in batchOperationMap) {
        if (operation === "userRoleUpdated") {
          let _res = userService.batchUpdateUserSiteRole(
            batchOperationMap[operation],
            requesterInfo
          );
          batchOperationPromiseHolder.push(_res);
        } else if (operation === "siteAccessRemoved") {
          let _res = userService.batchRemoveUserSiteAccess(
            batchOperationMap[operation],
            requesterInfo
          );
          batchOperationPromiseHolder.push(_res);
        } else if (operation === "newSiteAssigned") {
          let _res = userService.batchAssignNewSiteUserAccess(
            batchOperationMap[operation],
            requesterInfo
          );
          batchOperationPromiseHolder.push(_res);
        }
      }

      let operationResult = await Promise.all(batchOperationPromiseHolder);
      operationResult = operationResult.reduce((acm, curr) => {
        acm.push(...curr);
        return acm;
      }, []);
      operationResult.forEach((it) => {
        const {
        status,
        } = it;
        let code, message, operation, data;
        if (it.status !== "fulfilled") {
          code = it.reason.code;
          message= it.reason.message;
          operation = it.reason.operation
          data = it.reason.data
        }
        if (status === "rejected") {
          if (
            code === `E_SITE_ALREADY_ASSIGNED`||
            code === 'E_USER_SITE_REMOVE_LIMIT'
          ) {
            ResponseObject.WARNING.push({
              operation,
              data,
              message,
            });
          } else if (
            it.reason.code === `E_USER_NOT_FOUND` ||
            it.reason.code === "E_USER_SITE_NOT_FOUND" ||
            it.reason.code === "E_ROLE_NOT_FOUND"
          ) {
            ResponseObject.FAILED.push({
              operation,
              data,
              message,
            });
          } else {
            ResponseObject.EXCEPTION.push({
              operation,
              data,
              message,
            });
          }
        } else {
          const {operation, data, message} = it.value;
          ResponseObject.SUCCESS.push({
            operation,
            data,
            message
          });
        }
      });

      return exits.success(ResponseObject);
    } catch (e) {
        sails.log.error(e);
        return exits.serverError(e);
    }
  },
};
