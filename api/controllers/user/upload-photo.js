const self = require('../../services/user/user.service');

module.exports = {
  friendlyName: 'Upload - Photo',
  description : 'It updates the picture URL of a user',
  example: [
    `curl -X POST "http://localhost:1337/m2/user/v2/user/photo`,
  ],

  inputs: {
    userOrganization: {
      type: 'string',
      required: true,
      example: 'ssh'
    },
    picture: {
      type: 'string',
      required: true,
      example: 'https://anyrandomurl.com'
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: '<EMAIL>'
      }
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > uploadPhoto] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > uploadPhoto] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > uploadPhoto] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > uploadPhoto] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { userOrganization, picture } = inputs;
      let userId = inputs._userMeta['id'];
      if (!userId) return exits.badRequest({ problems: ['User ID is not present.'] });

      await self.update({ userId, userOrganization }, { picture });
      exits.success({ status: 'ok' });
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
