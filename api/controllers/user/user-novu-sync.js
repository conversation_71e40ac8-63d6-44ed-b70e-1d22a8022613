const userService = require('../../services/user/user.service');
module.exports = {
  friendlyName: '',
  description : '',
  example: [
    
  ],

  inputs: {
    userId: {
      type: 'string',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[User > NovuUserSync] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[User > NovuUserSync] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[User > NovuUserSync] forbidden Request!',
    },
  },

  fn: async function ({userId}, exits) {
    try{
      if (!userId.trim().length) return exits.badRequest({
        err: 'User Id must not be empty'
      });
      await userService.syncUserSubscriber(userId)
      return exits.success({
        message: 'User synced successfully.'
      });
    } catch(error) {
      if (error.code== 'E_USER_NOT_FOUND') {
        return exits.badRequest({
          err: error.message
        })
      } else {
        sails.log.error('Error!');
        sails.log.error(error);
        return exits.serverError({err:'Server has encountered an error.Please contact the administrator.'});
      }
    }
  }
};
