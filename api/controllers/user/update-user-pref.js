const userUtils = require('../../utils/user/utils');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.public');
const globalHelper = require('../../utils/globalhelper');
const authService = require('../../services/auth/auth.public');

module.exports = {
  friendlyName: 'Update-User-Preferences',
  description : 'It updates all the preferences of a user for a particular site.',
  example: [
    `curl -X PUT "http://localhost:1337/m2/user/v2/userPref`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName', '_site': 'ssh' },
      required: true
    },
    dj: {
      type: {},
      example: {
        'JouleRecipe': '0',
        'nudge': '0'
      }
    },
    djNotif: {
      type: {},
      example: {
        'JouleRecipe': '0',
        'nudge': '0'
      },
      required: true
    },
    mailConfig: {
      type: {},
      example: {
        'JouleRecipe': '0',
        'nudge': '0'
      },
      required: true
    },
    msgConfig: {
      type: {},
      example: {
        'JouleRecipe': '0',
        'nudge': '0'
      },
      required: true
    },
    unitPreference: {
      type: {},
      example: {
        'temperature': 'degC',
        'delTemperature': 'delC',
        'pressure': 'kPa',
        'length': 'm',
        'cons': 'kvah'
      },
      required: true
    },
    userOrganization: {
      type: 'string',
      example: 'Smart Joules',
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > updateUserPreferences] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > updateUserPreferences] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > updateUserPreferences] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > updateUserPreferences] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { _userMeta: userInfo, dj, djNotif, mailConfig, msgConfig, unitPreference } = inputs;
      let { id: userId, _site: siteId } = userInfo;
      if (!userId || !siteId) return exits.badRequest({ problems: ['Invalid request parameters.'] });

      let userSiteInfo = await userSiteMapService.findOne({ userId, siteId });
      if (userSiteInfo === undefined) return exits.forbidden({ problems: ['User Info for this site not found.'] });

      let preferences = {
        dj: globalHelper.toJson(dj),
        djNotif: globalHelper.toJson(djNotif),
        mailConfig: globalHelper.toJson(mailConfig),
        msgConfig: globalHelper.toJson(msgConfig),
        unitPreference: globalHelper.toJson(unitPreference),
      };

      let tokenData = userInfo;
      tokenData.unitPref = preferences.unitPreference;
      delete tokenData.exp;
      delete tokenData.iat;
      let token = authService.issueJWTToken(tokenData);

      userUtils.addDefaultPreferences(preferences);
      globalHelper.stringifyEachKeyOfObject(preferences);

      await userSiteMapService.update({ userId, siteId }, { ...preferences });
      return exits.success({ preferences, token });
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
