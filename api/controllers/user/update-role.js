const globalHelper = require('../../utils/globalhelper');
const userUtils = require('../../utils/user/utils');
const roleService = require('../../services/role/role.public');
const defaultPolicies = require('../../../config/policy.json');
const { notifyJouleTrackPublicRoom } = require('../../services/socket/socket.public');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'Update-Role',
  description : 'It updates an existing role',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    roleName: {
      type: 'string',
      required: true,
      example: 'admin'
    },
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: '<EMAIL>', _role: 'role', _site: 'siteId' },
    },
    pref: {
      type: 'json',
      required: true,
      example: {
        mailConfig: {
          JouleRecipe: {
            recipecomfortoptimization: 60
          }
        }
      }
    },
    policies: {
      type: 'json',
      required: true,
      example: {
        ACPlant: {
          'displayName': 'AC PLANT',
          'pageView': false,
          'subHeadingsList': [
            {
              'displayName': 'Command',
              'policyList': [
                {
                  'displayName': 'READ',
                  'routePolicyMap': {},
                  'hasAccess': false
                }
              ]
            }
          ]
        }
      }
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > updateRole] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > updateRole] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > updateRole] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > updateRole] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { roleName, pref, policies: policiesFromFE, _userMeta:{ id:userId, _site: siteId } } = inputs;
      let policiesBE, defaultPoliciesBE;

      userUtils.addDefaultPreferences(pref);

      const role = await roleService.findOne({roleName});
      if (!role) return exits.forbidden({ problems: ['This role does not exists.'] });
      if(role.isDeleted === '1') return exits.forbidden({problems: ['This role is deleted']});

      defaultPoliciesBE = userUtils.flattenPolicies(defaultPolicies);
      policiesBE = userUtils.flattenPolicies(policiesFromFE);

      for (let eachPolicy in defaultPoliciesBE) {
        if (policiesBE[eachPolicy]) defaultPoliciesBE[eachPolicy] = policiesBE[eachPolicy];
      }

      let roleInfoObject = {
        policies: policiesFromFE,
        policiesBE: defaultPoliciesBE,
        defpref: pref
      };
      let stringifiedObject = globalHelper.stringifyEachKeyOfObject(roleInfoObject);

      await roleService.update({roleName}, stringifiedObject);

      const auditPayload = {
        event_name: "state_update_role",
        user_id: userId,
        site_id: siteId,
        asset_id: 'Role-' + roleName,
        req: this.req,
        prev_state: role,
        curr_state: {roleName, stringifiedObject},
      };

      const updateEventData = {
        "event": "update",
        "data": {roleName, stringifiedObject},
      };

      await notifyJouleTrackPublicRoom(
        siteId,
        "userRoles",
        updateEventData
      );

      auditEventLogService.emit(auditPayload);

      return exits.success(stringifiedObject);

    } catch (e) {
      sails.log.error('[user > updateRole]',e);
      return exits.serverError(e);
    }
  }
};
