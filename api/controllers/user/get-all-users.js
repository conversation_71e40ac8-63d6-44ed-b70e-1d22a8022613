const userService = require('../../services/user/user.service');

module.exports = {
  friendlyName: 'Get All Users',
  description: '',
  example: [`curl -X GET "http://localhost:1337/m2/user/v2/users`],

  inputs: {
    _userMeta: {
      type: 'json',
      description: '',
      example: { id: 'userId', _role: 'role' },
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > get-all-users] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > get-all-users] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > get-all-users] unauthorized!',
    },
  },

  fn: async function (inputs, exits) {
    try {

      let users = await userService.find();
      if (users === undefined) {
        return exits.badRequest({ problems: ['No users found'] });
      }
      const filteredUsers = users.filter(user => user.isDeleted != true)
      return exits.success(filteredUsers);
    } catch (error) {
      sails.log.error('[user > get-all-users] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};
