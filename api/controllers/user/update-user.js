const globalHelper = require("../../utils/globalhelper");
const self = require("../../services/user/user.service");
const requestValidator = require("../../utils/user/update-user.util");

module.exports = {
  friendlyName: "Update-User",
  description: "It updates an existing user on Joule Track",
  example: [`curl -X GET "http://localhost:1337/`],

  inputs: {
    email: {
      type: "string",
      example: "<EMAIL>",
    },
    name: {
      type: "string",
      example: "<PERSON> Parker",
    },
    designation: {
      type: "string",
      example: "Software Development Engineer",
    },
    phone: {
      type: "string",
      example: "9999888822",
    },
    userId: {
      type: "string",
      example: "<EMAIL>",
    },
    userOrganization: {
      type: "string",
      required: true,
      example: "Smart Joules",
    },
    defaultSite: {
      type: "string",
      example: "ssh",
    },
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "<EMAIL>",
      },
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[user > UpdateUser] Server Error!",
    },
    badRequest: {
      responseType: "badRequest",
      description: "[user > UpdateUser] Bad Request!",
    },
    unauthorized: {
      responseType: "unauthorized",
      description: "[user > UpdateUser] unauthorized!",
    },
    forbidden: {
      responseType: "forbidden",
      description: "[user > UpdateUser] forbidden Request!",
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { name, phone, defaultSite, designation, email, userOrganization } = inputs;
      let userId = inputs._userMeta.id;
      requestValidator.validateUpdateUserInput(inputs);
      let updateObject = {
        name,
        phone,
        defaultSite,
        designation,
        email,
      };
      for (let property in updateObject) {
        if (globalHelper.isNullish(updateObject[property])) delete updateObject[property];
      }
      if (globalHelper.isNullish(updateObject)) return exits.success(updateObject);
      await self.update({ userId, userOrganization }, updateObject);
      return exits.success(updateObject);
    } catch (err) {
      sails.log.error("user > UpdateUser");
      sails.log.error(err);
      switch (err.code) {
        case "E_USER_NOT_EXIST":
          return exits.notFound({ err: err.message });
        case "E_INPUT_VALIDATION":
          return exits.badRequest({ err: err.message });
        default:
          return exits.serverError(err);
      }
    }
  },
};
