const self = require('../../services/user/user.service');
const jwtService = require('../../services/auth/auth.public');
const md5 = require('md5');

module.exports = {
  friendlyName: 'Reset-Password',
  description : 'It resets the password of the user',
  example: [
    `curl -X POST "http://localhost:1337/m2/user/v2/user/resetPassword`,
  ],

  inputs: {
    password: {
      type: 'string',
      required: true,
      example: 'password'
    },
    token: {
      type: 'string',
      required: true,
      example: 'JWT Token'
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > reset-password] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > reset-password] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > reset-password] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > reset-password] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { password, token } = inputs;
      let decodedToken;

      try {
        decodedToken = jwtService.verifyJWTToken(token);
      } catch (e) {
        return exits.forbidden({ problems: ['Invalid token'] });
      }

      let passwordHashmd5 = decodedToken.secret.split('-')[0];
      let userId = decodedToken.id;

      let user = await self.findOne({ userId });
      if (!user) return exits.forbidden({problems: ['This user does not exist']});

      if (md5(user.password + user.userId) !== passwordHashmd5) return exits.forbidden({ problems: ['Password already changed using this link'] });

      let { userOrganization } = user;

      await self.update({userId, userOrganization}, {password});

      return exits.success();
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
