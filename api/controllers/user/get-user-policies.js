const defaultPolicies = require('../../../config/policy.json');
const globalHelper = require('../../utils/globalhelper');
const self = require('../../services/user/user.service');
const userSiteMapService = require('../../services/userSiteMap/userSiteMap.public');
const deepmerge = require('deepmerge');

module.exports = {
  friendlyName: 'Get-User-Policies',
  description : 'It gets all the listed policies of a particular user',
  example: [
    `curl -X POST "http://localhost:1337/m2/user/v2/userPolicies`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[user > getUserPolicies] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[user > getUserPolicies] Bad Request!',
    },
    unauthorized: {
      responseType: 'unauthorized',
      description: '[user > getUserPolicies] unauthorized!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[user > getUserPolicies] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let userId = inputs._userMeta.id;
      if (!userId) return exits.forbidden({ problems: ['No valid user id is present in the token'] });

      let userSiteMapInfo = await userSiteMapService.find({ userId });
      if (userSiteMapInfo.length === 0) return exits.forbidden({ problems: ['Unable to get any roles for this user.'] });

      let siteIdRoleMap = {};
      for (let eachRole of userSiteMapInfo) {
        siteIdRoleMap[eachRole.siteId] = eachRole.role;
      }

      let rolesInfo = await self.getRolesInfo(siteIdRoleMap);
      if (rolesInfo.problems) return exits.forbidden(rolesInfo);
      let allPoliciesArray = [];
      for (let roleName in rolesInfo) {
        let policies = deepmerge(defaultPolicies, globalHelper.toJson(rolesInfo[roleName].policies));
        allPoliciesArray.push({
          roleName,
          policies
        });
      }
      return exits.success(allPoliciesArray);
    } catch (e) {
      sails.log.error(e);
      return exits.serverError(e);
    }
  }
};
