
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');
const selfUtils = require('../../utils/systemNodes/create-system-definition.util.js');

module.exports = {
  friendlyName: 'createSystemDefinition',
  description : 'This API is used to define a system hierarchy by defining different layer types along with their parents Ids. \
  To be called by developers only. Not integrated with FE.',
  example: [
    `curl --location --request POST 'http://localhost:1338/m2/configuration/v1/system/1/hierarchy' \
    --header 'Authorization: Bearer ' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "parentId": 3,
        "layerName": "Room",
        "layerType": "room"
    }'`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    systemId: {
      type: 'number',
      required: true,
      description: 'ID of the system where the layer is being added.',
    },
    parentId: {
      type: 'number',
      required: true,
      description: 'id in the system_nodes table of the parent layer. 0 if root layer.',
    },
    layerName: {
      type: 'string',
      required: true,
      description: 'Name of the layer.',
    },
    layerType: {
      type: 'string',
      required: true,
      description: 'Type of the layer. Used to distinguish from other layers. Needs to be unique in the system.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[systemNodes > createSystemDefinition] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[systemNodes > createSystemDefinition] Bad Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        systemId, parentId, layerName, layerType
      } = inputs;
      let $systemInfo = configurationHierarchyService.systems.findOne({ id: systemId });
      let $parentIdExists = configurationHierarchyService.systemNodes.findOne({
        system_id: systemId,
        parent_id: parentId
      });
      let $layerTypeExists = configurationHierarchyService.systemNodes.findOne({
        system_id: systemId,
        layer_type: layerType
      });
      let $parentDefinition = configurationHierarchyService.systemNodes.findOne({ id: parentId });

      let parentDefinition = await $parentDefinition;
      let layerTypeExists = await $layerTypeExists;
      let parentIdExists = await $parentIdExists;
      let systemInfo = await $systemInfo;
      let inputCheckOutput = selfUtils.checkInput(inputs, systemInfo, parentIdExists, layerTypeExists, parentDefinition);
      if(!inputCheckOutput.status)
        return exits.badRequest({
          "status": false,
          problems: inputCheckOutput.errors
        });

      await configurationHierarchyService.systemNodes.create({
        system_id: systemId,
        parent_id: parentId,
        layer_name: layerName,
        layer_type: layerType
      });

      return exits.success({ 'status': true });
    } catch(error) {
      sails.log.error('[systemNodes > createSystemDefinition] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
