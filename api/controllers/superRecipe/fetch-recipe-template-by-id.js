const recipeService = require('../../services/superRecipe/recipe.public');
module.exports = {
  friendlyName: 'fetch-recipe-template-by-id',
  description: 'fetch-recipe-template-by-id',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> fetch-recipe-template-by-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> fetch-recipe-template-by-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> fetch-recipe-template-by-id] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> fetch-recipe-template-by-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> fetch-recipe-template-by-id] Page created successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        id,
        siteId
      } = inputs;

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }
      const result = await recipeService.getActionRecipeTemplateInfoById(id);
      return exits.success(result);
    } catch (error) {
      sails.log.error('[superRecipe -> fetch-recipe-template-by-id]', error);
      return exits.serverError(error);
    }
  }
};
