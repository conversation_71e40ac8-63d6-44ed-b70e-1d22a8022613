const recipeService = require('../../services/superRecipe/recipe.service');

module.exports = {
  friendlyName: 'Sync recipe alert to smart alert engine',
  description: 'Synchronize an existing recipe alert with the smart alert engine',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId',
      },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    rid: {
      type: 'number',
      required: true,
      description: 'primary key of rid recipe',
      example: 1,
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> sync-recipe-alert] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> sync-recipe-alert] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> sync-recipe-alert] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> sync-recipe-alert] Recipe not found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> sync-recipe-alert] Recipe alert synced successfully',
    },
  },
  async fn(inputs, exits) {
    try {
      const {
        siteId,
        rid,
      } = inputs;
      const syncResult = await recipeService.syncSmartAlertRecipeById(siteId, rid);
      return exits.success(syncResult)
    } catch (error) {
      sails.log.error('[superRecipe -> sync-recipe-alert]', error);
      switch (error.code) {
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
