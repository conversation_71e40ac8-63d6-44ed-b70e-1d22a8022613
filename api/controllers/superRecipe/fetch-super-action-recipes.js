const SuperRecipeService = require('../../services/superRecipe/recipe.service');

module.exports = {
  friendlyName: 'Fetch Super Alert Recipe List of a site',
  description: 'Fetch all configured alert recipes for a site',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
    },
  },

  exits: {
    success: {
      description: 'Successfully retrieved alerts'
    },
    serverError: {
      statusCode: 500,
      description: 'Internal server error',
      responseType: 'serverError'
    }
  },

  fn: async function (inputs, exits) {
    const { siteId } = inputs;
    try {
      const result = await SuperRecipeService.fetchSuperActionRecipeListBySiteId(siteId);
      /**
       * Need to fetch the alert detail from vigilante database
       * fetch the subscribers
       * Notification Template
       */
      return exits.success(result);
    } catch (error) {
      sails.log.error('[list-configured-alerts] Error:', error);

      if (error.code === 'E_BAD_REQUEST') {
        return exits.badRequest({
          status: 'error',
          message: error.message
        });
      }

      return exits.serverError({
        status: 'error',
        message: 'Failed to list configured alerts',
        error: error.message
      });
    }
  }
};
