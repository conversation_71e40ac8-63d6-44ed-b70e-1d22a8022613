const recipeService = require('../../services/superRecipe/recipe.public');
const { validateRecipeInfo } = require('../../utils/super_recipe/create-recipe.util');

module.exports = {
  friendlyName: 'Create recipe',
  description: 'Create a new recipe',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId',
      },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    recipeInfo: {
      type: 'json',
      required: true,
      description: 'Payload containing recipe data for creation',
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> create-recipe] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> create-recipe] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> create-recipe] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> create-recipe] Not Found',
    },
    success: {
      statusCode: 201,
      description: '[superRecipe -> create-recipe] Recipe created successfully',
    },
  },
  async fn(inputs, exits) {
    try {
      const {
        siteId,
        recipeInfo,
        _userMeta: { id: userId },
      } = inputs;

      const recipeInfoPayload = {
        ...recipeInfo,
        site_id: siteId
      };
      validateRecipeInfo(recipeInfoPayload);
      const recipeInfoData = await recipeService.createRecipePayload(recipeInfoPayload, userId);
      if (_.isEmpty(recipeInfoData)) {
        return exits.badRequest({ message: 'Invalid recipe payload' });
      }

      if (recipeInfoData?.recipe_template_id) {
        const recipeTemplate = await recipeService.getActionRecipeInfoById(recipeInfoData.recipe_template_id, recipeInfoData.recipe_type, siteId);
        if (recipeTemplate.error) {
          return exits.badRequest({ message: `Provided Template reference id (${recipeInfoData.recipe_template_id}) not exists` });
        }
      }
      const newRecipe = await recipeService.createRecipe(recipeInfoData);
      if (newRecipe.recipe_type === "alert") {
        await recipeService.syncSmartAlertRecipeById(siteId, newRecipe.id)
      }
      return exits.success({
        message: 'Successfully created recipe',
        recipeInfo: newRecipe,
      });
    } catch (error) {
      sails.log.error('[superRecipe -> create-recipe]', error);
      switch (error.code) {
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
