const recipeService = require('../../services/superRecipe/recipe.public');
module.exports = {
  friendlyName: 'fetch-super-recipe-templates',
  description: 'fetch-all-super-recipes',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    type: {
      type: 'string',
      required: true,
      example: 'action',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> fetch-super-recipe-templates] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> fetch-super-recipe-templates] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> fetch-super-recipe-templates] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> fetch-super-recipe-templates] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> fetch-super-recipe-templates] Recipe Template Fetched successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        siteId,
        type
      } = inputs;

      if (!siteId) {
        return exits.badRequest({ message: 'siteId is required' });
      }

      if (!['action', 'alert'].includes(type)) {
        return exits.badRequest({ message: 'Invalid recipe type, can be action or alert only' });
      }

      const result = await recipeService.fetchAllSuperRecipeTemplatesBySiteId(siteId, type);
      return exits.success(result);
    } catch (error) {
      sails.log.error('[superRecipe -> fetch-super-recipe-templates]', error);
      switch (error.code) {
        case 'E_COMPONENT_NOT_FOUND':
          return exits.badRequest({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  }
};
