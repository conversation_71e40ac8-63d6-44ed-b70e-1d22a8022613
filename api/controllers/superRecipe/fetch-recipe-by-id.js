const recipeService = require('../../services/superRecipe/recipe.public');
module.exports = {
  friendlyName: 'fetch-recipe-by-id',
  description: 'fetch-recipe-by-id',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    type: {
      type: 'string',
      required: true,
      example: 'action',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> fetch-recipe-by-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> fetch-recipe-by-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> fetch-recipe-by-id] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> fetch-recipe-by-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> fetch-recipe-by-id] Page created successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        id,
        siteId,
        type
      } = inputs;

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }

      if (!['action', 'alert'].includes(type)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }

      const result = await recipeService.getActionRecipeInfoById(id, type, siteId);
      return exits.success(result);
    } catch (error) {
      sails.log.error('[superRecipe -> fetch-recipe-by-id]', error);
      return exits.serverError(error);
    }

  }
};
