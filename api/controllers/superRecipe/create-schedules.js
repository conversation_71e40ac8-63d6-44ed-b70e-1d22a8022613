const { validateScheduleData } = require('../../utils/super_recipe/schedule.util');
const scheduleService = require('../../services/superRecipe/schedule/schedule.public');
const recipeService = require('../../services/superRecipe/recipe.public');
const { deleteSuperRecipeScheduleFromController } = require('../../services/superRecipe/lib/recipe.iot.communication.interface');

module.exports = {
  friendlyName: 'Create Schedules',
  description: 'Create Super Recipe Schedules',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId',
      },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    recipeId: {
      type: 'string',
      required: true,
      example: '42',
    },
    scheduleInfo: {
      type: 'json',
      required: true,
      description: 'Payload containing schedule data for creation',
    },
    subscribers: {
      type: 'json',
      description: 'Payload containing subscribers data for creation',
    },
    escalationInMin: {
      type: 'number',
      description: 'The escalation time in minutes',
    },
    escalatedTo: {
      type: 'string',
      description: 'The escalation time in minutes',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> create-schedules] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> create-schedules] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> create-schedules] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> create-schedules] Not Found',
    },
    success: {
      statusCode: 201,
      description: '[superRecipe -> create-schedules] Schedules created successfully',
    },
  },
  fn: async function(inputs, exits)  {
    try {
      const {
        recipeId,
        siteId,
        scheduleInfo,
        subscribers,
        escalationInMin,
        escalatedTo,
      } = inputs;

      const transactionId = this.req?.headers?.["x-transaction-id"];

      if (!recipeId || !siteId) {
        return exits.badRequest({ message: 'recipeId/siteId is required' });
      }

      const recipe = await recipeService.findOne({
        id: recipeId,
        status: 1,
        is_recipe_template: 0,
        site_id: siteId,
      });

      if (!recipe) {
        return exits.badRequest({ message: `No active recipe exists with id=${recipeId} and siteId=${siteId}` });
      }
      const scheduleInfoData = scheduleInfo.map((item) => ({
        ...item,
        recipe_id: +recipeId,
      }));

      validateScheduleData(scheduleInfoData);

      const processedSchedules = await Promise.all(
        scheduleInfoData.map(async (item) => {
          if (item.id) {
            await deleteSuperRecipeScheduleFromController({
              siteId,
              runOn: recipe?.run_on,
              recipeId: recipe?.rid,
              scheduleId: item.id,
              transactionId,
            });
            return scheduleService.update(item.id, item);
          } else {
            return scheduleService.create(item);
          }
        }),
      );

      if (!_.isEmpty(subscribers)) {
        await recipeService.syncSmartAlertMetaDetailById(siteId, recipeId, subscribers, escalatedTo, escalationInMin);
      }

      return exits.success({
        message: 'Schedules successfully processed',
        processedSchedules
      });

    } catch (error) {
      sails.log.error('[superRecipe -> create-schedules]', error);
      switch (error.code) {
        case 'E_MISSING_FIELDS':
          return exits.badRequest({ message: error.message });
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: error.message });
        case 'E_INVALID_TIME_RANGE':
          return exits.badRequest({ err: error.message });
        case 'E_FORBIDDEN':
          return exits.forbidden({ message: 'You are not authorized to perform this action.' });
        case 'E_NOT_FOUND':
          return exits.notFound({ message: error.message });
        case 'E_DB_ERROR':
          return exits.serverError(error);
        default:
          return exits.serverError(error);
      }

    }

  }
};
