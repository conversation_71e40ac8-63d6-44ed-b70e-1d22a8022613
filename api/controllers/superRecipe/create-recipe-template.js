const { validateRecipeTemplateInfo } = require('../../utils/super_recipe/create-recipe.util');
const recipeService = require('../../services/superRecipe/recipe.public');

module.exports = {
  friendlyName: 'create-recipe-template',
  description: 'Creates a template for recipe',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId',
      },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    recipeInfo: {
      type: 'json',
      required: true,
      description: 'Payload containing recipe data for creation',
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> create-recipe-template] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> create-recipe-template] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> create-recipe-template] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> create-recipe-template] Not Found',
    },
    success: {
      statusCode: 201,
      description: '[superRecipe -> create-recipe-template] Recipe Template created successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        siteId,
        recipeInfo,
        _userMeta: { id: userId },
      } = inputs;
      const recipeInfoPayload = {
        ...recipeInfo,
        site_id: siteId
      };
      validateRecipeTemplateInfo(recipeInfoPayload);
      const recipeInfoData = await recipeService.createRecipePayload(recipeInfoPayload, userId);

      if (_.isEmpty(recipeInfoData)) {
        return exits.badRequest({ message: 'Invalid recipe payload' });
      }

      const newRecipe = await recipeService.createRecipe(recipeInfoData);
      return exits.success({
        message: 'Successfully created recipe template',
        recipeInfo: newRecipe,
      });
    } catch (error) {
      sails.log.error('[superRecipe -> create-recipe-template]', error);
      switch (error.code) {
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  }
};
