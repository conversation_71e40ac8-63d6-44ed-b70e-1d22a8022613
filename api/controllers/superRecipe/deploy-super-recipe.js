const recipeService = require('../../services/superRecipe/recipe.public');
const RecipeIotPayloadBuilder = require('../../services/superRecipe/lib/recipe.iot.payload.builder');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');
const auditEventLogService = require("../../services/auditEventLog/auditEventLog.public");

module.exports = {
  friendlyName: 'deploy-super-recipe',
  description: 'deploy-super-recipe',
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "<EMAIL>", _role: "admin", _site: "ash-tri" },
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    recipeType: {
      type: 'string',
      required: true,
      example: 'action',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> deploy-super-recipe] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> deploy-super-recipe] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> deploy-super-recipe] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> deploy-super-recipe] Not Found',
    },
    entityUnprocessable: {
      statusCode: 422
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> deploy-super-recipe] Request Registered successfully',
    },
  },
  fn: async function(inputs, exits)  {
    try {
      const {
        id,
        siteId,
        recipeType,
        _userMeta: { id: userId },
      } = inputs;

      const transactionId = this.req?.headers?.["x-transaction-id"];

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }
      if (!['action', 'alert'].includes(recipeType)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }

      const recipeData = await recipeService.getActionRecipeInfoById(id, recipeType, siteId);
      if (_.isEmpty(recipeData?.schedules)) {
        return exits.badRequest({ message: 'There is no active schedules for this recipe ' + id });
      }

      const builder = new RecipeIotPayloadBuilder({ ...recipeData, transactionId });
      let payload = builder.buildPayload();

      if (recipeData?.is_deployed === 1) {
        let { recipeInfo, ...cleanPayload } = payload;
        payload = cleanPayload;
      }

      await RecipeIotCommunicationInterface.deploySuperRecipe(siteId, recipeData?.run_on, payload);
      sails.log.info(JSON.stringify(payload, null, 2));

      const auditPayload = {
        event_name: "state_deploy_super_recipe",
        user_id: userId,
        site_id: siteId,
        asset_id: "recipe_" + id,
        req: this.req,
        prev_state: recipeData,
        curr_state: payload,
      };

      auditEventLogService.emit(auditPayload);
      return exits.success({
        message: `Recipe ${id} deploy request registered successfully`,
        payload,
        runOn: recipeData?.run_on
      });
    } catch (error) {
      sails.log.error('[superRecipe -> deploy-super-recipe]', error);
      if (error?.code?.includes('[AWS-IOT-CORE-SERVICE')) {
        return exits.entityUnprocessable({
          message: error?.message,
          code: error?.code
        });
      }
      return exits.serverError(error);
    }
  }
};
