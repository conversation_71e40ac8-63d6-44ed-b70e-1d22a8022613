const recipeService = require('../../services/superRecipe/recipe.public');

module.exports = {
  friendlyName: 'delete-recipe-template-by-id',
  description: 'Deletes recipe by id',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    type: {
      type: 'string',
      required: true,
      example: 'action/alert',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> delete-recipe-template-by-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> delete-recipe-template-by-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> delete-recipe-template-by-id] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> delete-recipe-template-by-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> delete-recipe-template-by-id] Template deleted successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        id,
        siteId,
      } = inputs;

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }
      const recipeData = await recipeService.findOne({
        id,
        status: 1,
        site_id: siteId,
        is_recipe_template: 1
      });
      if (_.isEmpty(recipeData)) {
        return exits.badRequest({ message: 'Recipe Template not found' });
      }

      const response = await Promise.all([recipeService.delete(id), recipeService.delete_children_recipes({ parent_recipe_id: id }), recipeService.deleteTemplateLinks(id)]);
      return exits.success({
        message: `Recipe Template with id=${id} deleted`,
        response
      });
    } catch (error) {
      sails.log.error('[superRecipe -> delete-recipe-template-by-id]', error);
      if (error.code === 'E_NOT_FOUND') {
        return exits.badRequest({ message: error.message });
      }
      return exits.serverError(error);
    }

  }
};
