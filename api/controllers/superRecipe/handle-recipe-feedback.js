const { handleRecipeFeedbackEvent } = require('../../services/superRecipe/lib/feedback.event.handler');
module.exports = {
  friendlyName: 'handle-recipe-feedback',
  description: 'Handle feedback for super recipe actions',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'ash-tri',
    },
    data: {
      type: 'ref',
      required: false,
      description: 'Payload structure (e.g., { rid: string })',
    },
    recipeInfo: {
      type: 'ref',
      required: false,
      description: 'recipe info structure containing rid',
    },
    scheduleInfo: {
      type: 'ref',
      required: false,
      description: 'schedule info array that may contain rid',
    },
    operation: {
      type: 'string',
      required: false,
    },
    status: {
      type: 'ref',
      required: false,
    },
    func: {
      type: 'string',
      required: false,
    },
  },

  exits: {
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> handle-recipe-feedback] Bad request',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> handle-recipe-feedback] Feedback received successfully',
    },
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> handle-recipe-feedback] Internal server error',
    },
  },

  fn: async (inputs, exits) => {
    try {
      const {
        siteId,
        data,
        func,
        operation,
      } = inputs;

      const rid = inputs?.recipeInfo?.rid || inputs?.data?.[0]?.rid || inputs?.data?.rid;

      if (func) {
        sails.log.info(`[Feedback][${func}] site=${siteId}, operation=${operation}, rid=${rid}`);
      } else {
        sails.log.info(`[Feedback][operation] site=${siteId}, rid=${rid}`);
      }

      const response = await handleRecipeFeedbackEvent(inputs);

      return exits.success(response);
    } catch (err) {
      sails.log.error('[superRecipe -> handle-recipe-feedback]', err);
      return exits.serverError(err);
    }
  }
};
