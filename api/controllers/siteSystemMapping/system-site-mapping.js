
const configurationHierarchyService = require('../../services/configurationHierarchy/configurationHierarchy.service');
const sitePublic = require('../../services/site/site.public');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'enableSystemSiteMapping',
  description : 'To be called to enable an existing system on a site. Requires the system definition to be complete.',
  example: [
    `curl --location --request POST 'http://localhost:1338/m2/configuration/v1/site/mgch/system/1' \
    --header 'Authorization: Bearer ' \
    --data-raw ''`,
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    systemId: {
      type: 'number',
      required: true,
      example: 14,
      description: 'ID of the system being enabled on this site.',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'mgch',
      description: 'siteId of the site.',
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[siteSystemMapping > enableSystemSiteMapping] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[siteSystemMapping > enableSystemSiteMapping] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[siteSystemMapping > enableSystemSiteMapping] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try{
      const {
        systemId, siteId, _userMeta:{ id:userId }
      } = inputs;

      // Input Checks
      let $doesSiteExist = sitePublic.siteExists(siteId);
      let $systemInfo = configurationHierarchyService.systems.findOne({
        id: systemId
      });
      let $isSystemAlreadyEnabled = configurationHierarchyService.siteSystemMapping.findOne({
        site_id: siteId,
        system_id:systemId
      });
      let doesSiteExist = await $doesSiteExist;
      if(!doesSiteExist)
        return exits.badRequest({
          "status": false,
          "message": "Site does not exist!"
        });
      let systemInfo = await $systemInfo;
      if(!systemInfo)
        return exits.badRequest({
          "status": false,
          "message": "System does not exist!"
        });
      let isSystemAlreadyEnabled = await $isSystemAlreadyEnabled;
      if(isSystemAlreadyEnabled)
        return exits.badRequest({
          "status": false,
          "message": "System already enabled on site!"
        });

      // Enabling system on site
      await configurationHierarchyService.siteSystemMapping.create({
        site_id: siteId,
        system_id: systemId
      });

      const auditPayload = {
        event_name: "state_create_ibms-site-system-mapping",
        user_id: userId,
        site_id: siteId,
        asset_id: 'ibms-system-'+ systemId,
        req: this.req,
        prev_state: null,
        curr_state: systemInfo,
      };

      auditEventLogService.emit(auditPayload);

      return exits.success({
        "status": true,
        "message": `Site '${siteId}' enabled with systemId: '${systemId}' and systemName: '${systemInfo.name}'`
      });
    } catch(error) {
      sails.log.error('[siteSystemMapping > enableSystemSiteMapping] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
