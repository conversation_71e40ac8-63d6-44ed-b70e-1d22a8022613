
const templateService = require('../../services/template/template.service');
const globalHelper = require('../../utils/globalhelper');

module.exports = {
  friendlyName: 'getTemplate',
  description: 'Fetching templates(predefined recipes)',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],


  inputs: {
    templateType: {
      type: 'string',
      example: 'enum[action,alert]',
      isIn: ['action', 'alert'],
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[template > getTemplate] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[template > getTemplate] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[template > getTemplate] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      let { templateType } = inputs;
      let templates = await templateService.find(templateType);
      return exits.success(templates);
    } catch (error) {
      sails.log.error('[template > getTemplate] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
