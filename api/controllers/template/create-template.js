
const templateService = require('../../services/template/template.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/template/create-template.util.js');

module.exports = {
  friendlyName: 'createTemplate',
  description: 'To create predefined recipes from postman for now.',
  example: [
    `curl -X POST -H "Content-Type: application/json" --data \' {
      "templateType": "action",
      "templateCategory": "miscellaneous",
      "title": "High discharge pressure (>1000 kPa)",
      "description": "Discharge pressure greater than 1000 kPa",
      "expression": "P||$1||$1||#1||$3||:val1||$2||,||:val2||$2",
      "attributeDriverName": { "#1": "chiller.0.dispressure" },
      "attributeValue": { ":val1": 1000, ":val2": 80},
      "operatorName": { "$1": "(", "$2": ")", "$3": ">"}
    }\' 0:1337/m2/recipe/v2/template`,
  ],

  inputs: {
    templateType: {
      type: 'string',
      description: 'type of template recipe',
      example: 'enum[action,alert]',
      required: true,
      isIn: ['action', 'alert']
    },
    templateCategory: {
      type: 'string',
      example: 'frequencyPush',
      required: true
    },
    title: {
      type: 'string',
      example: 'Name of recipe',
      required: true
    },
    description: {
      type: 'string',
      example: 'detail of recipe',
      required: true
    },
    expression: {
      type: 'string',
      example: '||#1||$3||:val1',
      description: 'Recipe formula expression',
      required: true
    },
    attributeDriverName: {
      type: {},
      example: { '#1': 'chiller.0.tonnage' },
      required: true
    },
    attributeValue: {
      type: {},
      example: { ':val1': 20 },
      required: true
    },
    operatorName: {
      type: {},
      example: {
        $1: '-', $2: '/', $3: '>', $4: '(', $5: ')',
      },
      required: true
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[template > createTemplate] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[template > createTemplate] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[template > createTemplate] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {
    try {
      const templateObject = selfUtils.buildInitialPacket(inputs);
      await templateService.create(templateObject);
      return exits.success(templateObject);

    } catch (error) {
      sails.log.error('[template > createTemplate] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
