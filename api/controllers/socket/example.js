const socketService = require('../../services/socket/socket.public');
const eventService = require('../../services/event/event.public');

module.exports = {
  friendlyName: 'Example js for socket service',
  description: 'This file has example usage of how to call socket service',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true // this fields auto add authentication
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[auth > registerSocket] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[auth > registerSocket] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[auth > registerSocket] forbidden!',
    },
  },

  fn: async function (inputs, exits) {

    let decodedAuthToken = inputs._userMeta;
    try {
      eventService.publish('test/publish', {random: 'data'});
      // await socketService.notifyServiceOnJouleTrack('sjo-del', 'recipies', 'recipe', { data: { recipe: '4bfdf9e7-0cde-4426-9556-b66dbfdef865' }, 'event': 'deployRecipe' });
      // await socketService.notifyServiceOnJouleTrack('sjo-del', 'recipies', 'recipe', { data: { recipe: '4bfdf9e7-0cde-4426-9556-b66dbfdef865' }, 'event': 'deleteRecipe' });
      // await socketService.notifyServiceOnJouleTrack('sjo-del', 'recipies', 'recipe', { data: { recipe: '4bfdf9e7-0cde-4426-9556-b66dbfdef865', switchOff: '0' }, 'event': 'startStopRecipe' });
      // await socketService.notifyServiceOnJouleTrack('sjo-del', 'recipies', 'recipe', { data: { recipe: '4bfdf9e7-0cde-4426-9556-b66dbfdef865', sid: '' }, 'event': 'deleteSchedule' });
      // await socketService.notifyJouleTrackPublicRoom("mgch", "asdf", "data"); // every one by default on site "ssh" joins a public room. So publishing on here will be sent to everyone on the site
      // await socketService.sendSocketMessageToUserIds([decodedAuthToken.id], "mgch" ,"asdf", "data2"); // send the data to user [USER_ID] on topic "asdf"
      // await socketService.notifyServiceOnJouleTrack('mgch', 'recipies', 'topic-recipedelete', 'data3');
      // await socketService.sendSocketMessageToSocketIds([this.req.socket.id], "asdf", "data3"); // send the data to user [USER_ID] on topic "asdf"


      return exits.success({
        'added': true,
      });

    } catch (error) {
      sails.log.error('[auth > registerSocket] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
