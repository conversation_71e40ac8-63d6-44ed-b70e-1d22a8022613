
const authService = require('../../services/auth/auth.service');
const cacheService = require('../../services/cache/cache.public');
const socketService = require('../../services/socket/socket.service');

module.exports = {
  friendlyName: 'registerSocket',
  description: '',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    _userMeta: {
      type: {},
      example: { 'id': 'userName' },
      required: true // this fields auto add authentication
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[auth > registerSocket] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[auth > registerSocket] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[auth > registerSocket] forbidden!',
    },
  },

  fn: async function (inputs, exits) {

    let decodedAuthToken = inputs._userMeta;
    try {
      // now every socket route should check this, so it should be policy
      // that policy should just add socketId to body to avoid using
      if (!this.req.socket.id) {
        return exits.forbidden({ problems: ['This is socket only route'] });
      }

      let clientBrowserHash = decodedAuthToken._h_;
      let userId = decodedAuthToken.id;
      let siteId = decodedAuthToken._site;
      let roleName = decodedAuthToken._role;
      let socketId = this.req.socket.id;

      // Add user to cache
      cacheService.sadd(clientBrowserHash, socketId).then(() => {
        return cacheService.expire(clientBrowserHash, 86400); // 86400 = 24 hours
      }).then(() => {
        return cacheService.set('u_' + clientBrowserHash, userId);
      }).then(() => {
        return cacheService.expire('u_' + clientBrowserHash, 86400); // 86400 = 24 hours
      }).then(() => {
        return cacheService.listAddAndTrim(`usersockets_${siteId}_${userId}`, socketId, 50); // add socketID to list of user
      }).then(() => {
        return cacheService.expire(`usersockets_${siteId}_${userId}`, 86400); // 86400 = 24 hours
      }).catch(e => {
        sails.log.error(e);
        throw new Error('Cache Service Error: ', e);
      });

      // get list of services this role can access
      await authService.getAccessibleServicesByRoleName(roleName).then((services) => {
        // join the relevant rooms this role needs to join .. A roomName = `jt_SITEID_SERVICENAME`
        let $joinRooms = services.map(service =>
          socketService.joinJouleTrack(socketId, `jt_${siteId}_${service}`)
        );
        return Promise.all($joinRooms);
      }).then(() => {
        return;
      }).catch(e => {
        if (e.name === 'Error')
          return exits.badRequest({ 'problems': [e] });
        else
          return exits.forbidden({ 'problems': [e] });
      });

      return exits.success({
        'added': true,
      });

    } catch (error) {
      sails.log.error('[auth > registerSocket] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};
