const moment = require('moment-timezone');

const ALERT_STATUS = {
  DELETED: 0,
  ACTIVE: 1,
  PAUSED: 2
};

module.exports = {
  async syncAlertTemplateStatus(rid, lifecycleEvent, siteId) {
    try {
      if (!rid || !lifecycleEvent) {
        throw new Error('Recipe ID and lifecycle event are required');
      }

      const targetStatus = getTargetStatusFromLifecycleEvent(lifecycleEvent);
      const db = sails.getDatastore(process.env.SMART_ALERT_DB_NAME || 'vigilante');

      return await db.transaction(async (conn) => {
        // Find alert templates associated with this recipe
        const alertTemplates = await sails.models.alerttemplate.find({
          observer_execution_ref_id: rid,
          site_id: siteId
        }).usingConnection(conn);

        if (!alertTemplates || alertTemplates.length === 0) {
          return {
            success: true,
            message: `No alert templates found for recipe ${rid}`,
            updated: []
          };
        }

        const updateResults = [];

        for (const template of alertTemplates) {
          // Skip if already in target status
          if (template.status === targetStatus) {
            continue;
          }

          // Update alert template status
          const updatedTemplate = await sails.models.alerttemplate.updateOne({ id: template.id })
            .set({
              status: targetStatus,
              updatedAt: moment.tz('UTC').toISOString()
            })
            .usingConnection(conn);

          if (updatedTemplate) {
            // Update related entities only if alert template update succeeded
            await updateRelatedEntities(template.id, targetStatus, conn);

            updateResults.push({
              alertTemplateId: template.id,
              previousStatus: template.status,
              newStatus: targetStatus,
              lifecycleEvent
            });
          }
        }

        return {
          success: true,
          message: `Successfully synced ${updateResults.length} alert templates for recipe ${rid}`,
          updated: updateResults
        };
      });

    } catch (error) {
      sails.log.error(`[SmartAlertStatusSync] Error syncing alert template status for rid: ${rid}`, error);
      throw error;
    }
  }
};

function getTargetStatusFromLifecycleEvent(lifecycleEvent) {
  switch (lifecycleEvent.toLowerCase()) {
    case 'deploy':
    case 'play':
      return ALERT_STATUS.ACTIVE;

    case 'undeploy':
    case 'pause':
      return ALERT_STATUS.PAUSED;

    case 'delete':
      return ALERT_STATUS.DELETED;

    default:
      throw new Error(`Unknown recipe lifecycle event: ${lifecycleEvent}`);
  }
}

async function updateRelatedEntities(alertTemplateId, targetStatus, conn) {
  // Update related alert inventory - only update existing records
  const alertInventories = await sails.models.alertinventory.find({
    alert_template_ref_id: alertTemplateId
  }).usingConnection(conn);

  if (alertInventories.length > 0) {
    await sails.models.alertinventory.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }

  // Update related alert subscribers - only update existing records
  const alertSubscribers = await sails.models.alertsubscribers.find({
    alert_template_ref_id: alertTemplateId
  }).usingConnection(conn);

  if (alertSubscribers.length > 0) {
    await sails.models.alertsubscribers.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }

  // Update related notification message templates - only update existing records
  const notificationTemplates = await sails.models.notificationmessagetemplate.find({
    alert_template_ref_id: alertTemplateId
  }).usingConnection(conn);

  if (notificationTemplates.length > 0) {
    await sails.models.notificationmessagetemplate.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }
}
