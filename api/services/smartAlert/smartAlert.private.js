const RecipeAlertBuilder = require('./lib/RecipeAlertBuilder');
const recipeService = require('../recipe/recipe.public');
module.exports = {
  registerRecipeInSmartAlert: async function (rid) {
    const recipeBuilder = await RecipeAlertBuilder.init(rid);
    return recipeBuilder.register();
  },
  syncRecipeInSmartAlert: async function (rid) {
    const recipeBuilder = await RecipeAlertBuilder.init(rid);
    return await recipeBuilder.sync();
  },
  deleteRecipeFromSmartAlert: async function (rid) {
    const recipeBuilder = await RecipeAlertBuilder.init(rid);
    return await recipeBuilder.delete();
  },
  syncRecipeInSmartAlertBySiteId: async function (siteId) {
    const errorResponses = [];
    const syncRecipes = [];

    const recipesBySiteId = await recipeService.find({ siteId });
    const alertRecipes = recipesBySiteId.filter(
      (recipe) => recipe?.actionable && JSON.parse(recipe?.actionable)?.[0]?.type === 'alert',
    );

    if (_.isEmpty(alertRecipes)) {
      return [];
    }

    for (const alertRecipe of alertRecipes) {
      try {
        const syncResult = await this.syncRecipeInSmartAlert(alertRecipe.rid);
        syncRecipes.push(syncResult);
      } catch (syncError) {
        errorResponses.push({
          recipeId: alertRecipe.rid,
          error: syncError,
        });
      }
    }

    return {
      success: syncRecipes.length,
      failed: errorResponses.length,
      syncRecipes,
      errors: errorResponses,
    };
  },
};
