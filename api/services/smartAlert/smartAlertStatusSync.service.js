const moment = require('moment-timezone');

/**
 * Smart Alert Status Synchronization Service
 * 
 * This service handles synchronization of AlertTemplate status based on recipe lifecycle events.
 * It supports both super recipes (PostgreSQL) and legacy recipes (DynamoDB).
 * 
 * Status mapping:
 * - 0: Deleted/Inactive (recipe deleted)
 * - 1: Active (recipe deployed/playing)
 * - 2: Paused (recipe paused/undeployed)
 */

const ALERT_STATUS = {
  DELETED: 0,
  ACTIVE: 1,
  PAUSED: 2
};

const RECIPE_LIFECYCLE_EVENTS = {
  DEPLOY: 'deploy',
  UNDEPLOY: 'undeploy',
  PLAY: 'play',
  PAUSE: 'pause',
  DELETE: 'delete'
};

class SmartAlertStatusSyncService {
  constructor() {
    this.db = sails.getDatastore(process.env.SMART_ALERT_DB_NAME || 'vigilante');
  }

  /**
   * Sync alert template status based on recipe lifecycle event
   * @param {string} rid - Recipe ID
   * @param {string} lifecycleEvent - Recipe lifecycle event (deploy, undeploy, play, pause, delete)
   * @param {string} siteId - Site ID
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Sync result
   */
  async syncAlertTemplateStatus(rid, lifecycleEvent, siteId, options = {}) {
    try {
      if (!rid || !lifecycleEvent) {
        throw new Error('Recipe ID and lifecycle event are required');
      }

      const targetStatus = this._getTargetStatusFromLifecycleEvent(lifecycleEvent);
      
      sails.log.info(`[SmartAlertStatusSync] Syncing alert template status for rid: ${rid}, event: ${lifecycleEvent}, target status: ${targetStatus}`);

      return await this.db.transaction(async (conn) => {
        // Find alert templates associated with this recipe
        const alertTemplates = await AlertTemplate.find({
          observer_execution_ref_id: rid,
          site_id: siteId || { '!=': null }
        }).usingConnection(conn);

        if (!alertTemplates || alertTemplates.length === 0) {
          sails.log.info(`[SmartAlertStatusSync] No alert templates found for rid: ${rid}`);
          return {
            success: true,
            message: `No alert templates found for recipe ${rid}`,
            updated: []
          };
        }

        const updateResults = [];

        for (const template of alertTemplates) {
          // Skip if already in target status
          if (template.status === targetStatus) {
            sails.log.info(`[SmartAlertStatusSync] Alert template ${template.id} already in target status ${targetStatus}`);
            continue;
          }

          // Update alert template status
          const updatedTemplate = await AlertTemplate.updateOne({ id: template.id })
            .set({
              status: targetStatus,
              updatedAt: moment.tz('UTC').toISOString()
            })
            .usingConnection(conn);

          // Update related alert inventory status
          await this._updateRelatedAlertInventory(template.id, targetStatus, conn);

          // Update related alert subscribers status
          await this._updateRelatedAlertSubscribers(template.id, targetStatus, conn);

          // Update notification message templates status
          await this._updateRelatedNotificationTemplates(template.id, targetStatus, conn);

          updateResults.push({
            alertTemplateId: template.id,
            previousStatus: template.status,
            newStatus: targetStatus,
            lifecycleEvent
          });

          sails.log.info(`[SmartAlertStatusSync] Updated alert template ${template.id} from status ${template.status} to ${targetStatus}`);
        }

        return {
          success: true,
          message: `Successfully synced ${updateResults.length} alert templates for recipe ${rid}`,
          updated: updateResults
        };
      });

    } catch (error) {
      sails.log.error(`[SmartAlertStatusSync] Error syncing alert template status for rid: ${rid}`, error);
      throw error;
    }
  }

  /**
   * Sync alert template status for super recipe by ID
   * @param {number} recipeId - Super recipe ID
   * @param {string} lifecycleEvent - Recipe lifecycle event
   * @param {string} siteId - Site ID
   * @returns {Promise<Object>} - Sync result
   */
  async syncAlertTemplateStatusByRecipeId(recipeId, lifecycleEvent, siteId) {
    try {
      // Get recipe details to find the RID
      const recipe = await RecipeInfo.findOne({ id: recipeId, site_id: siteId });
      
      if (!recipe) {
        throw new Error(`Super recipe not found with id: ${recipeId}`);
      }

      return await this.syncAlertTemplateStatus(recipe.rid, lifecycleEvent, siteId);
    } catch (error) {
      sails.log.error(`[SmartAlertStatusSync] Error syncing alert template status for recipe ID: ${recipeId}`, error);
      throw error;
    }
  }

  /**
   * Get target alert status based on recipe lifecycle event
   * @param {string} lifecycleEvent - Recipe lifecycle event
   * @returns {number} - Target alert status
   * @private
   */
  _getTargetStatusFromLifecycleEvent(lifecycleEvent) {
    switch (lifecycleEvent.toLowerCase()) {
      case RECIPE_LIFECYCLE_EVENTS.DEPLOY:
      case RECIPE_LIFECYCLE_EVENTS.PLAY:
        return ALERT_STATUS.ACTIVE;
      
      case RECIPE_LIFECYCLE_EVENTS.UNDEPLOY:
      case RECIPE_LIFECYCLE_EVENTS.PAUSE:
        return ALERT_STATUS.PAUSED;
      
      case RECIPE_LIFECYCLE_EVENTS.DELETE:
        return ALERT_STATUS.DELETED;
      
      default:
        throw new Error(`Unknown recipe lifecycle event: ${lifecycleEvent}`);
    }
  }

  /**
   * Update related alert inventory status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedAlertInventory(alertTemplateId, targetStatus, conn) {
    await AlertInventory.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }

  /**
   * Update related alert subscribers status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedAlertSubscribers(alertTemplateId, targetStatus, conn) {
    await AlertSubscribers.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }

  /**
   * Update related notification message templates status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedNotificationTemplates(alertTemplateId, targetStatus, conn) {
    await NotificationMessageTemplate.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }

  /**
   * Batch sync alert templates for multiple recipes
   * @param {Array} recipes - Array of recipe objects with rid and lifecycleEvent
   * @param {string} siteId - Site ID
   * @returns {Promise<Object>} - Batch sync result
   */
  async batchSyncAlertTemplateStatus(recipes, siteId) {
    try {
      const results = [];
      
      for (const recipe of recipes) {
        try {
          const result = await this.syncAlertTemplateStatus(recipe.rid, recipe.lifecycleEvent, siteId);
          results.push({ rid: recipe.rid, ...result });
        } catch (error) {
          sails.log.error(`[SmartAlertStatusSync] Error in batch sync for rid: ${recipe.rid}`, error);
          results.push({
            rid: recipe.rid,
            success: false,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: `Batch sync completed for ${recipes.length} recipes`,
        results
      };
    } catch (error) {
      sails.log.error('[SmartAlertStatusSync] Error in batch sync', error);
      throw error;
    }
  }
}

module.exports = new SmartAlertStatusSyncService();
