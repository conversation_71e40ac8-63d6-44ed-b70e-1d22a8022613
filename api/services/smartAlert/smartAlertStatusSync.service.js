const moment = require('moment-timezone');

const ALERT_STATUS = {
  DELETED: 0,
  ACTIVE: 1,
  PAUSED: 2
};

class SmartAlertStatusSyncService {
  async syncAlertTemplateStatus(rid, lifecycleEvent, siteId) {
    try {
      if (!rid || !lifecycleEvent) {
        throw new Error('Recipe ID and lifecycle event are required');
      }

      const targetStatus = this._getTargetStatusFromLifecycleEvent(lifecycleEvent);

      sails.log.info(`[SmartAlertStatusSync] Syncing alert template status for rid: ${rid}, event: ${lifecycleEvent}, target status: ${targetStatus}`);

      // Find alert templates associated with this recipe using default datastore
      const alertTemplates = await sails.models.alerttemplate.find({
        observer_execution_ref_id: rid,
        site_id: siteId
      }).meta({ datastore: 'default' });

      sails.log.info(`[SmartAlertStatusSync] Found ${alertTemplates?.length || 0} alert templates for rid: ${rid}`);

      if (!alertTemplates || alertTemplates.length === 0) {
        return {
          success: true,
          message: `No alert templates found for recipe ${rid}`,
          updated: []
        };
      }

      const updateResults = [];

      for (const template of alertTemplates) {
        // Skip if already in target status
        if (template.status === targetStatus) {
          sails.log.info(`[SmartAlertStatusSync] Template ${template.id} already in target status ${targetStatus}, skipping`);
          continue;
        }

        sails.log.info(`[SmartAlertStatusSync] About to update template ${template.id} from status ${template.status} to ${targetStatus}`);

        const updatedTemplate = await sails.models.alerttemplate.updateOne({ id: template.id })
          .set({
            status: targetStatus,
            updatedAt: moment.tz('UTC').toISOString()
          })
          .meta({ datastore: 'default' });

        sails.log.info(`[SmartAlertStatusSync] UpdateOne result:`, updatedTemplate ? 'SUCCESS' : 'FAILED');

        if (!updatedTemplate) {
          sails.log.error(`[SmartAlertStatusSync] Failed to update alert template status for template ${template.id}`);
          continue;
        }

        await this._updateRelatedAlertInventory(template.id, targetStatus);
        await this._updateRelatedAlertSubscribers(template.id, targetStatus);
        await this._updateRelatedNotificationTemplates(template.id, targetStatus);

        updateResults.push({
          alertTemplateId: template.id,
          previousStatus: template.status,
          newStatus: targetStatus,
          lifecycleEvent
        });
      }

      const result = {
        success: true,
        message: `Successfully synced ${updateResults.length} alert templates for recipe ${rid}`,
        updated: updateResults
      };

      sails.log.info(`[SmartAlertStatusSync] Sync completed for rid: ${rid}`, result);
      return result;

    } catch (error) {
      sails.log.error(`[SmartAlertStatusSync] Error syncing alert template status for rid: ${rid}`, error);
      throw error;
    }
  }



  /**
   * Get target alert status based on recipe lifecycle event
   * @param {string} lifecycleEvent - Recipe lifecycle event
   * @returns {number} - Target alert status
   * @private
   */
  _getTargetStatusFromLifecycleEvent(lifecycleEvent) {
    switch (lifecycleEvent.toLowerCase()) {
      case 'deploy':
      case 'play':
        return ALERT_STATUS.ACTIVE;

      case 'undeploy':
      case 'pause':
        return ALERT_STATUS.PAUSED;

      case 'delete':
        return ALERT_STATUS.DELETED;

      default:
        throw new Error(`Unknown recipe lifecycle event: ${lifecycleEvent}`);
    }
  }

  /**
   * Update related alert inventory status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedAlertInventory(alertTemplateId, targetStatus) {
    await sails.models.alertinventory.update({
      alert_template_ref_id: alertTemplateId
    })
      .set({ status: targetStatus })
      .meta({ datastore: 'default' });
  }

  /**
   * Update related alert subscribers status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedAlertSubscribers(alertTemplateId, targetStatus) {
    await sails.models.alertsubscribers.update({
      alert_template_ref_id: alertTemplateId
    })
      .set({ status: targetStatus })
      .meta({ datastore: 'default' });
  }

  /**
   * Update related notification message templates status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedNotificationTemplates(alertTemplateId, targetStatus) {
    await sails.models.notificationmessagetemplate.update({
      alert_template_ref_id: alertTemplateId
    })
      .set({ status: targetStatus })
      .meta({ datastore: 'default' });
  }


}

module.exports = new SmartAlertStatusSyncService();
