const moment = require('moment-timezone');

const ALERT_STATUS = {
  DELETED: 0,
  ACTIVE: 1,
  PAUSED: 2
};

class SmartAlertStatusSyncService {
  constructor() {
    this.db = sails.getDatastore(process.env.SMART_ALERT_DB_NAME || 'vigilante');
  }

  /**
   * Sync alert template status based on recipe lifecycle event
   * @param {string} rid - Recipe ID
   * @param {string} lifecycleEvent - Recipe lifecycle event (deploy, undeploy, play, pause, delete)
   * @param {string} siteId - Site ID
   * @returns {Promise<Object>} - Sync result
   */
  async syncAlertTemplateStatus(rid, lifecycleEvent, siteId) {
    try {
      if (!rid || !lifecycleEvent) {
        throw new Error('Recipe ID and lifecycle event are required');
      }

      const targetStatus = this._getTargetStatusFromLifecycleEvent(lifecycleEvent);

      return await this.db.transaction(async (conn) => {
        // Find alert templates associated with this recipe
        const alertTemplates = await sails.models.alerttemplate.find({
          observer_execution_ref_id: rid,
          site_id: siteId
        }).usingConnection(conn);

        if (!alertTemplates || alertTemplates.length === 0) {
          return {
            success: true,
            message: `No alert templates found for recipe ${rid}`,
            updated: []
          };
        }

        const updateResults = [];

        for (const template of alertTemplates) {
          // Skip if already in target status
          if (template.status === targetStatus) {
            continue;
          }

          await sails.models.alerttemplate.updateOne({ id: template.id })
            .set({
              status: targetStatus,
              updatedAt: moment.tz('UTC').toISOString()
            })
            .usingConnection(conn);

          await this._updateRelatedAlertInventory(template.id, targetStatus, conn);

          await this._updateRelatedAlertSubscribers(template.id, targetStatus, conn);

          await this._updateRelatedNotificationTemplates(template.id, targetStatus, conn);

          updateResults.push({
            alertTemplateId: template.id,
            previousStatus: template.status,
            newStatus: targetStatus,
            lifecycleEvent
          });
        }

        return {
          success: true,
          message: `Successfully synced ${updateResults.length} alert templates for recipe ${rid}`,
          updated: updateResults
        };
      });

    } catch (error) {
      sails.log.error(`[SmartAlertStatusSync] Error syncing alert template status for rid: ${rid}`, error);
      throw error;
    }
  }



  /**
   * Get target alert status based on recipe lifecycle event
   * @param {string} lifecycleEvent - Recipe lifecycle event
   * @returns {number} - Target alert status
   * @private
   */
  _getTargetStatusFromLifecycleEvent(lifecycleEvent) {
    switch (lifecycleEvent.toLowerCase()) {
      case 'deploy':
      case 'play':
        return ALERT_STATUS.ACTIVE;

      case 'undeploy':
      case 'pause':
        return ALERT_STATUS.PAUSED;

      case 'delete':
        return ALERT_STATUS.DELETED;

      default:
        throw new Error(`Unknown recipe lifecycle event: ${lifecycleEvent}`);
    }
  }

  /**
   * Update related alert inventory status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedAlertInventory(alertTemplateId, targetStatus, conn) {
    await sails.models.alertinventory.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }

  /**
   * Update related alert subscribers status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedAlertSubscribers(alertTemplateId, targetStatus, conn) {
    await sails.models.alertsubscribers.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }

  /**
   * Update related notification message templates status
   * @param {number} alertTemplateId - Alert template ID
   * @param {number} targetStatus - Target status
   * @param {Object} conn - Database connection
   * @private
   */
  async _updateRelatedNotificationTemplates(alertTemplateId, targetStatus, conn) {
    await sails.models.notificationmessagetemplate.update({
      alert_template_ref_id: alertTemplateId
    })
    .set({ status: targetStatus })
    .usingConnection(conn);
  }


}

module.exports = new SmartAlertStatusSyncService();
