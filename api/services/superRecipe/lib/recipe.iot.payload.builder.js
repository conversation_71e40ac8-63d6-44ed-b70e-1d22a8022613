const moment = require('moment-timezone');
const { v4: uuidv4 } = require('uuid');

class RecipeIotPayloadBuilder {
  constructor(recipeInfo) {
    this.recipeInfo = recipeInfo;
  }

  buildActionAlert(actions, uniqId, category, priority, params) {
    return actions.sort((a, b) => a.execution_order - b.execution_order)
      .map(action => {
        const basePayload = {
          title: this.recipeInfo?.title,
          description: this.recipeInfo.description,
          type: this.recipeInfo?.recipe_type,
          notify: _.isEmpty(this.recipeInfo.notify)
            ? [this.recipeInfo.created_by]
            : this.recipeInfo.notify,
          smslist: _.isEmpty(this.recipeInfo.smslist) ? ["**********"] : this.recipeInfo.smslist,
          accountable: _.isEmpty(this.recipeInfo.notify)
            ? [this.recipeInfo.created_by]
            : this.recipeInfo.notify,
          priority,
          uniqId: action.uniqid || uuidv4(),
          category,
        };

        if (this.recipeInfo?.recipe_type === "alert") {
          return {
            ...basePayload,
            parent: extractParentFromParams(params),
          };
        }


        if (this.recipeInfo?.recipe_type === 'action') {
          return {
            ...basePayload,
            controls_rel: {},
            command: action.command,
            did: action.did,
            parent: action.parent,
            value: action.value,
          };
        }

        return basePayload;
      });
  }

  buildChildrenRecipes() {
    const children = {};
    this.recipeInfo.children_recipes.sort((a, b) => a.execution_order - b.execution_order)
      .forEach(recipe => {
        const blockId = recipe.block_type !== 'else' ? `if/${recipe.execution_order}` : recipe.block_type;
        children[blockId] = {
          everyMinuteTopics: recipe.everyMinuteTopics || [],
          topics: recipe.everyMinuteTopics || [],
          recipe: recipe.formula,
          syncAsyncAction: 'True',
          startNow: 'True',
          maxDataNeeded: recipe.observation_time.toString(),
          rid: `${this.recipeInfo.rid}`,
          notRun: '[]',
          blockId: recipe?.uniqid,
          appType: this.recipeInfo.app_type,
          actionAlert: this.buildActionAlert(
            this.recipeInfo?.recipe_type === "action" ? recipe.actions : [{}],
            recipe.uniqid + '/' + blockId,
            [this.recipeInfo.recipe_category],
            this.recipeInfo.priority,
            recipe.params
          ),
        };
      });
    return children;
  }

  buildPayload() {
    return {
      operation: "recipeInit",
      transactionId: this.recipeInfo?.transactionId,
      scheduleInfo: this.recipeInfo.schedules.map(schedule => {
        const startTime = schedule.time_ranges?.[0]?.start_time || '00:00:00';
        const endTime = schedule.time_ranges?.[schedule.time_ranges.length - 1]?.end_time || '23:59:00';

        const startDateStr = moment(schedule.start_date).format('YYYY-MM-DD');
        const endDateStr = moment(schedule.end_date).format('YYYY-MM-DD');

        return {
          rid: this.recipeInfo.rid,
          id: schedule.id || '',
          cron: schedule.cron || '* * * * *',
          startdate: moment(`${startDateStr} ${startTime}`).format('YYYY-MM-DD HH:mm'),
          enddate: moment(`${endDateStr} ${endTime}`).format('YYYY-MM-DD HH:mm'),
        };
      }),
      recipeInfo: {
        runOn: this.recipeInfo.run_on,
        failsafe: "{}",
        dependentOnOthers: [...new Set(this.recipeInfo.dependentOnOthers.map(String))],
        controllers: [...new Set(this.recipeInfo.controllers.map(String))],
        startNow: "True",
        maxDataNeeded: this.recipeInfo.run_interval.toString(),
        rid: this.recipeInfo.rid,
        switchOff: this.recipeInfo.switch_off.toString(),
        notRun: "[]",
        appType: this.recipeInfo.app_type,
        actionAlert: [
          {
            category: [this.recipeInfo.recipe_category],
            parent: this.recipeInfo?.children_recipes?.[0]?.actions?.[0]?.parent,
            title: this.recipeInfo.title,
            description: this.recipeInfo.description,
            priority: this.recipeInfo.priority,
            type: this.recipeInfo.app_type,
            childrenRecipe: this.buildChildrenRecipes(),
          },
        ],
      },
    };
  }
}

/**
 * @description Extracts the parent value (prefix before dot) from the first param entry.
 * @param params An object with key-value pairs like { "#1": "ash-tri_32.status" }
 * @returns The parent string (e.g., "ash-tri_32") or undefined if not found.
 */
function extractParentFromParams(params) {
  const firstParamValue = Object.values(params)[0];
  return firstParamValue?.split('.')?.[0];
}


module.exports = RecipeIotPayloadBuilder;


