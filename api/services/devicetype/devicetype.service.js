const { sortDriverConfigParameter, throwExceptionDriverConfigNotFound } = require('../../utils/deviceType/deviceType.utils');
const devicetypeservice = require('./devicetype.private');
const globalHelper = require('../../utils/globalhelper');
/**
 * @description get all device type config of a requested driver type
 * @param {String} deviceType
 */
const getDriverDeviceType = async (deviceType, deviceClass) => {
  const driverConfigArr = await devicetypeservice.find({
    deviceType,
    class: deviceClass
  });
  if (_.isEmpty(driverConfigArr)) throwExceptionDriverConfigNotFound(deviceType);
  return  driverConfigArr
}

module.exports = {
  'find': devicetypeservice.find,
  'findOne': devicetypeservice.findOne,
  'update': devicetypeservice.update,
  'create': devicetypeservice.create,
  getAllDeviceTypes: async function() {
      const deviceTypeData = await devicetypeservice.find();
      if (!deviceTypeData) return {};
      const deviceTypeCat = deviceTypeData.reduce((acc, curr) => {
        const { deviceType, driverType, class: className, driverName } = curr;
        if (!deviceType || !driverType || !className) return acc;
        const key = `${deviceType}_${driverType}`;
        if (!acc.hasOwnProperty(className)) {
          acc[className] = {
            [deviceType]: {
              [driverType]: {
                name: driverName
              }
            }
          };
          return acc;
        }

        if (acc[className][deviceType]){
          acc[className][deviceType][driverType] = {
            name: driverName
          };
        } else {
          acc[className][deviceType] = {
            [driverType]: {
              name: driverName
            }
          } ;
        }
        return acc;
      }, {});
      return deviceTypeCat;
  },
  getComponentDriverConfig: async function(deviceType) {
    const [driverConfigArr, controlConfigList] = await Promise.all([
      getDriverDeviceType(deviceType, "components"),
      DriverControlsRelationshipConfig.find({
        where: {
          status: 1,
          deviceType,
        },
        select: ["controlAbbr"],
      }),
    ]);
    const driverConfig = {deviceType};

    /*
    * Component's table stores only configured data and command parameter. But
    * we have to merge configured data and command parameters that is available at driver level also
    * */
    driverConfig.controls =  controlConfigList;

    driverConfig.data = (function mergeDriverDataParameterWithConfiguredCommand({
      driversList,
    }) {
      const mergedDataParams = {}
      const uniqueAbbr = {};
      for (const driverConfig of driversList) {
        let { parameters: driverDataParams } = driverConfig;

        for (const params of driverDataParams) {
          const driverParam = _.isObject(params) && params || JSON.parse(params)

          const {
            abbr: key,
            type,
          } = driverParam;
          if (type !== 'data') continue;
          const mergedParam = { key };
          if (mergedDataParams.hasOwnProperty(key)) continue;
          Object.assign(mergedParam, driverParam);

          const data = dataParamFactoryBuilder(mergedParam)
          mergedDataParams[key] = data

        }

        function dataParamFactoryBuilder(packet){
          const obj = {
            dau: '',
            deviceId: '',
            displayName: '',
            expression: '',
            group: '',
            key: '',
            max: '',
            min: '',
            origin: '',
            paramGroup: '',
            rogueMax: '',
            rogueMin: '',
          };
          for (const key in packet) {
            if (obj.hasOwnProperty(key)) {
              obj[key] = packet[key];
            }
          }
          return obj;
        }

      }
      return Object.values(mergedDataParams);
    })({
      driversList: driverConfigArr,
    });

    driverConfig.isValidDataAbbr = function (dataAbbr) {
      const { data } = this;
      return data && Array.isArray(data) && data.map((it) => it.key)
        .includes(dataAbbr);
    };
    driverConfig.isValidControlAbbr = function (controlAbbrToCheck) {
      if (!this.controls) return false;
      const {
       controls ,
      } = this;
      return (
        controls &&
        Array.isArray(controls) &&
       controls.map((it) => it.controlAbbr).includes(controlAbbrToCheck)
      );
    };
    sortDriverConfigParameter(driverConfig.data)
    sortDriverConfigParameter(driverConfig.controls)
    Object.freeze(driverConfig);
    return driverConfig;
  },
  getDeviceDriverConfig: async function(deviceType) {
    const driverConfigArr = await getDriverDeviceType(deviceType, 'devices');
    const driverConfig = {deviceType}

    driverConfig.data = (function mergeDriverDataParameterWithConfiguredCommand({
      driversList,
    }) {
      const mergedDataParams = {}
      const uniqueAbbr = {};
      for (const driverConfig of driversList) {
        let { parameters: driverDataParams } = driverConfig;

        for (const params of driverDataParams) {
          const driverParam = _.isObject(params) && params || JSON.parse(params)

          const {
            abbr: key,
            type,
          } = driverParam;
          if (type !== 'data') continue;
          const mergedParam = { key };
          if (mergedDataParams.hasOwnProperty(key)) continue;
          Object.assign(mergedParam, driverParam);

          const data = dataParamFactoryBuilder(mergedParam)
          mergedDataParams[key] = data

        }

        function dataParamFactoryBuilder(packet){
          const obj = {
            dau: '',
            deviceId: '',
            displayName: '',
            expression: '',
            group: '',
            key: '',
            max: '',
            min: '',
            origin: '',
            paramGroup: '',
            rogueMax: '',
            rogueMin: '',
          };
          for (const key in packet) {
            if (obj.hasOwnProperty(key)) {
              obj[key] = packet[key];
            }
          }
          return obj;
        }

      }
      return Object.values(mergedDataParams);
    })({
      driversList: driverConfigArr,
    });

    driverConfig.isValidDataAbbr = function (dataAbbr) {
      const { data } = this;
      return data && Array.isArray(data) && data.map((it) => it.key)
        .includes(dataAbbr);
    };
    sortDriverConfigParameter(driverConfig.data)
    Object.freeze(driverConfig);
    return driverConfig;
  },
  getDeviceTypes:async function() {
    const deviceTypes = await DeviceTypes.find({});
    return deviceTypes.map((deviceType) => {
      let tempArray = globalHelper.toArray(deviceType.parameters);
      deviceType.parameters = tempArray ? tempArray : [];
      deviceType.params = deviceType.parameters.map(globalHelper.toJson);
      return deviceType;
    });
  }
};
