/* eslint-disable no-undef */
const globalHelpers = require('../../utils/globalhelper');
const utils = require('../../utils/event/utils');
const rabbitMQService = require('../rabbitmq/rabbitmq.public');

/* PUBLISH_QUEUE is the name of the rabbit mq queue */
const notification = new rabbitMQService.consumerWithAckQueue('PUBLISH_QUEUE');
const initConnection = notification.initConnection();

const events = require('events');
const eventEmitter = new events.EventEmitter();

function notifyRespectiveService(topic, message) {
  let serviceToNotify = utils.findServiceToNotifyFromTopic(topic);
  if (serviceToNotify === undefined) {
    sails.log.error('Mqtt topic belongs to unidentified service ', topic);
    return;
  } else {
    sails.log('New Message for ', serviceToNotify);
  }
  eventEmitter.emit(serviceToNotify, {
    topic,
    message
  });
  return;
}

module.exports = {
  /**
   * Publish data to topic
   * @param {string} topic Topic to send/publish data to
   * @param {string} message Stringified object of data to send to subscriber
   * @returns {promise} Success or not in piblishing true/false
   */
  publish: async function (topic, message) {
    try {
      const data = {
        topic: topic,
        message: message,
      };

      /* the message is sent to the rabbit mq queue on an eventService publish */
      initConnection.then((_) => {
        notification.sendMessage(data);
      });
      return true;
    } catch (e) {
      sails.log.error('eventservice::publish ', e);
      return false;
    }
  },
  eventEmitter,
  notifyRespectiveService,
};
