const flaverr = require("flaverr");

module.exports = {
  sortDriverConfigParameter:function(parameters){
    return parameters.sort((a, b) => {
      // Check if "expression" is empty for both items
      if (!a.expression && !b.expression) {
        return 0; // Both expressions are empty, no change in order
      } else if (!a.expression) {
        return 1; // Only "a" has an empty expression, move "b" up
      } else if (!b.expression) {
        return -1; // Only "b" has an empty expression, move "a" up
      }

      // Both "expression" properties are not empty, compare them
      const expressionComparison = a.expression.localeCompare(b.expression);

      if (expressionComparison === 0) {
        // If "expression" is the same, compare "displayName" property
        return a.displayName.localeCompare(b.displayName);
      }

      return expressionComparison;
    });

  },
  /**
   * 
   * @param {Arr} driverType 
   * @param {String} deviceType 
   */
  throwExceptionDriverConfigNotFound: function(deviceType) {
    throw flaverr('E_DRIVER_CONFIG_NOT_FOUND', new Error(`Driver config does not exist for  device: ${deviceType}`));
  }
}