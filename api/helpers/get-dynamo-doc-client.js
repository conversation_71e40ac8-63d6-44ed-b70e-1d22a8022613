module.exports = {
  friendlyName: "create a new dynamo db client",
  inputs: {},

  exits: {
    success: {
      outputFriendlyName: "Email delivery report",
      outputDescription: "A dictionary of information about what went down.",
      outputType: {
        loggedInsteadOfSending: "boolean",
      },
    },
  },
 
  fn: async function (inputs) {
    const AWS = require("aws-sdk");
    let documentClient = new AWS.DynamoDB.DocumentClient();
    return await documentClient;
  },
};
