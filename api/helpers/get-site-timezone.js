const siteService = require("../services/site/site.public");
const cacheService = require("../services/cache/cache.service");
const { convertTimezone } = require("../utils/globalhelper");

const EXPIRY = 15 * 24 * 60 * 60; // 15d

module.exports = {
  friendlyName: "Get site timezone",
  description: "Fetches and formats the timezone for a site using siteId, utilizing cache.",

  inputs: {
    siteId: {
      type: "string",
      required: true,
      example: "sjo-del",
      description: "The unique identifier for the site.",
    },
    timezoneFormat: {
      type: "string",
      defaultsTo: "Z",
      description: "The format to return the timezone in.",
      // example: {
      //   Z: "+05:30 or -04:00",                // ISO 8601 time offset format
      //   ZZ: "+0530 or -0400",                 // ISO 8601 basic time offset format
      //   z: "abbreviation of the timezone name - e.g., EDT, IST",  // Timezone abbreviation
      //   utcOffsetInMinute: "330",             // UTC offset in minutes (e.g., 330 for IST)
      //   utcOffsetInNumber: "330,-160",        // UTC offset as a number (positive/negative values)
      // },
    },
  },

  exits: {
    success: {
      description: "Timezone found and formatted successfully.",
    },
    notFound: {
      description: "No site found with the provided siteId.",
      responseType: "notFound",
    },
    invalidTimezone: {
      description: "The provided timezone is invalid.",
      responseType: "badRequest",
    },
    invalidFormat: {
      description: "The provided timezone format is invalid.",
      responseType: "badRequest",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, timezoneFormat } = inputs;
      const cachedKey = `site:${siteId}:timezone`;
      let timezone = await cacheService.get(cachedKey);
      if (timezone) return exits.success(convertTimezone(timezone, timezoneFormat));

      const siteDetails = await siteService.findOne({ siteId });
      if (!siteDetails) return exits.notFound(`No site found with siteId: ${siteId}`);

      timezone = siteDetails.timezone;
      await cacheService.set(cachedKey, timezone);
      await cacheService.expire(cachedKey, EXPIRY);
      return exits.success(convertTimezone(timezone, timezoneFormat));
    } catch (error) {
      sails.log.error(
        `[get-site-timezone] Error fetching or formatting timezone for siteId: ${siteId}`,
        error
      );
      switch (error.code) {
        case "E_INVALID_TIMEZONE":
          return exits.invalidTimezone(error.message);
        case "E_INVALID_FORMAT":
          return exits.invalidFormat(error.message);
        default:
          return exits.error(error);
      }
    }
  },
};
