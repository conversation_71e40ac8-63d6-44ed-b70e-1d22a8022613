const moment = require('moment');
module.exports = {
    friendlyName: "Get current timestamp ist",
    sync: true,
    inputs: {
      timestampFormat: {
        type: 'string',
        example: 'YYYY-MM-DD HH:mm:ss',
        defaultsTo: 'YYYY-MM-DD HH:mm:ss'
      }
    },
    exits: {
    },
   
    fn: function ({timestampFormat}) {
     return moment().utcOffset("330m").format(timestampFormat);
    },
  };
  