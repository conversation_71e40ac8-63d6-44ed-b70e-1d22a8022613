const flaverr = require('flaverr');
const AWS = require('aws-sdk');

module.exports = {
  friendlyName: 'Generate next device id',

  description: 'Generate the next available device ID by atomically incrementing the device count. This prevents race conditions when multiple devices are created simultaneously.',

  exits: {
    success: {
      description: 'Device ID generated successfully.',
      outputType: 'number'
    },
    serverError: {
      description: 'Unable to generate device ID.'
    }
  },

  fn: async function () {
    AWS.config.update({
      region: process.env.REGION,
    });
    const dynamoDb = new AWS.DynamoDB.DocumentClient();

    // Use DynamoDB's atomic ADD operation to increment the counter
    const params = {
      TableName: 'dyanmokeystores',
      Key: {
        key: 'currentDeviceIdCount'
      },
      UpdateExpression: 'ADD #count :inc',
      ExpressionAttributeNames: {
        '#count': 'count'
      },
      ExpressionAttributeValues: {
        ':inc': 1
      },
      ReturnValues: 'UPDATED_NEW'
    };

    const result = await dynamoDb.update(params).promise();

    if (result && result.Attributes && result.Attributes.count) {
      return parseInt(result.Attributes.count);
    }

    throw flaverr('E_INTERNAL_SERVER_ERROR', new Error('Unable to generate device ID'));
  }
};