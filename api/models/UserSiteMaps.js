/**
 * UserSiteMap.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {
  primaryKey: 'userId',
  'attributes': {
    'userId': {
      'description': 'hash',
      'type': 'string'
    },
    'siteId': {
      'description': 'range',
      'type': 'string'
    },
    'role': {
      'type': 'string'
    },
    'djNotif': {
      'type': 'string'
    },
    'mailConfig': {
      'type': 'string'
    },
    'msgConfig': {
      'type': 'string'
    },
    'dj': {
      'type': 'string'
    },
    'unitPreference': {
      'type': 'string'
    },
    'phone': {
      'type': 'string'
    },
    'joulePulse': {
      'type': 'json'
    },
    'consumptionPageEMList': {
      'type': 'json'
    },
    'status': {
      'type': 'number',
      'defaultsTo': 1
    }
  }
};
