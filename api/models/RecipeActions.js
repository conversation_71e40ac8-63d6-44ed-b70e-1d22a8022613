module.exports = {
  datastore: 'postgres',
  tableName: 'recipe_actions',
  primaryKey: 'id',

  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    command: {
      type: 'string',
    },
    did: {
      type: 'string',
    },
    parent: {
      type: 'string',
    },
    value: {
      type: 'string',
    },
    status: {
      type: 'number',
      defaultsTo: 1
    },
    uniqid: {
      type: 'string',
      unique: true,
    },
    execution_order: {
      type: 'number',
    },
    recipe_id: {
      model: 'childrenrecipes',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
  },
};
