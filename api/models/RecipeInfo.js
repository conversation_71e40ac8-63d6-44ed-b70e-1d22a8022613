module.exports = {
  datastore: 'postgres',
  tableName: 'recipe_info',
  primaryKey: 'id',

  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    rid: {
      type: 'string',
    },
    site_id: {
      type: 'string',
    },
    app_type: {
      type: 'string',
    },
    components_type: {
      type: 'json',
    },
    template_title: {
      type: 'string',
    },
    run_interval: {
      type: 'number',
    },
    run_on: {
      type: 'number',
    },
    switch_off: {
      type: 'number',
      defaultsTo: 0,
    },
    is_deployed: {
      type: 'number',
      defaultsTo: 0,
      description: 'Indicates whether the recipe has been deployed'
    },
    recipe_category: {
      type: 'string',
    },
    created_by: {
      type: 'string',
    },
    title: {
      type: 'string',
    },
    description: {
      type: 'string',
    },
    last_updated_by: {
      type: 'string',
    },
    priority: {
      type: 'number',
    },
    status: {
      type: 'number',
      defaultsTo: 1
    },
    notify: {
      type: 'json',
    },
    smslist: {
      type: 'json',
    },
    recipe_type: {
      type: 'string',
    },
    is_recipe_template: {
      type: 'number',
      defaultsTo: 0,
    },
    recipe_template_id: {
      type: 'number',
      allowNull: true,
      description: 'Allows null for non-template recipes'
    },
    dependentOnOthers: {
      type: 'json',
    },
    controllers: {
      type: 'json',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
    children_recipes: {
      collection: 'childrenrecipes',
      via: 'parent_recipe_id',
    },
    schedules: {
      collection: 'recipeschedule',
      via: 'recipe_id',
      description: 'A list of schedules associated with this recipe',
    },
  },
};
