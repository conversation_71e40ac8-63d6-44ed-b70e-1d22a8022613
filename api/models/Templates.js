/**
 * @module Templates.js
 * @description :: This table stores draft recipies taht can be instantly create in a site.
 */

module.exports = {
    primaryKey: 'templateType',
    'attributes': {
        'templateType': {
            'type': 'string',
            'description': 'hash',
            'required': true,
            'example': 'enum[action,alert]'
        },
        'templateId': {
            'type': 'string',
            'description': 'range',
            'example': 'frequencyPush_1619496345000'
        },
        'title': {
            'type': 'string',
        },
        'description': {
            'type': 'string',
        },
        'expression': {
            'type': 'string',
        },
        'attributeDriverName': {
            'type': 'json'
        },
        'attributeValue': {
            'type': 'json'
        },
        'operatorName': {
            'type': 'json'
        },
    },
};
