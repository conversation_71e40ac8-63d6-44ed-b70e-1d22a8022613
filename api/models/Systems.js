module.exports = {
    datastore:'postgres',
    tableName: 'systems',
    primaryKey: 'id',
    attributes: {
      id: { type: 'number', autoIncrement: true, },
      name: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'Floor',
        description: 'Name of the node.',
      },
      component_list: {
        type: 'json',
        example: '["chiller","ahu"]',
        description: 'List of accepted components in the system',
      },
      createdAt: { type: 'ref', columnType: 'timestamp', autoCreatedAt: true, },
      updatedAt: { type: 'ref', columnType: 'timestamp', autoUpdatedAt: true, },
    },
  };
  