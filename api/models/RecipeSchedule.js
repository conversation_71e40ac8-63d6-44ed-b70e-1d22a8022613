module.exports = {
  datastore: 'postgres',
  tableName: 'recipe_schedule',
  primaryKey: 'id',

  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    recipe_id: {
      model: 'recipeinfo',
      required: true,
      description: 'Foreign key linking to the recipe_info table',
    },
    repeat_type: {
      type: 'string',
      isIn: ['daily', 'custom', 'dailySlot'],
      required: true,
    },
    status: {
      type: 'number',
      isIn: [0, 1],
      required: true,
    },
    cron: {
      type: 'string',
      description: 'store cron schedules',
      required: true,
    },
    start_date: {
      type: 'ref',
      columnType: 'timestamp',
      required: true,
    },
    end_date: {
      type: 'ref',
      columnType: 'timestamp',
    },
    is_deployed: {
      type: 'number',
      defaultsTo: 0,
      description: 'Set to 1 once the schedule is deployed',
    },
    time_ranges: {
      type: 'json',
      columnType: 'jsonb',
      required: true,
      description: 'Array of objects with start_time and end_time in HH:MM:SS format',
    },
    uniqId: {
      columnName: 'uniqid',
      type: 'string',
      unique: true,
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
  },
};
