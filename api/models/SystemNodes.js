module.exports = {
    datastore:'postgres',
    tableName: 'system_nodes',
    primaryKey: 'id',
    attributes: {
      id: { type: 'number', autoIncrement: true, },
      system_id: {
        type: 'number',
        // required: true,
        description: 'ID found in system table',
      },
      parent_id: {
        type: 'number',
        // required:true,
        example: 'Id of the parent itself.'
      },
      layer_name: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'Floor',
        description: 'Name of the layer.',
      },
      layer_type: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'floor',
        description: 'Type of the layer.',
      },
      createdAt: { type: 'ref', columnType: 'timestamp', autoCreatedAt: true, },
      updatedAt: { type: 'ref', columnType: 'timestamp', autoUpdatedAt: true, },
    },
  };
