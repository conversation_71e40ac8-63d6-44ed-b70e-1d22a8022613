/**
 * Versions.js
 *
 * @description :: Stores version specific configration information
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {
  primaryKey: "version",

  attributes: {
    version: {
      type: "string",
      description: "hash",
    },
    deviceType: {
      type: "string",
      description: "range",
    },
    dockerApplication: {
      type: "json",
    },
    dockerFirmware: {
      type: "json",
    },
    dockerJouleBox: {
      type: "json",
    },
    gitRepoApplication: {
      type: "json",
    },
    gitRepoCICD: {
      type: "json",
    },
    gitRepoFirmware: {
      type: "json",
    },
    gitRepoHostServices: {
      type: "json",
    },
    gitRepoJouleBox: {
      type: "json",
    },
    gitRepoRoutingService: {
      type: "json",
    },
    repoList: {
      type: "json",
      columnType: "stringSet",
    },
  },
};
