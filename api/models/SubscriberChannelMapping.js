module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'subscriber_channel_mapping',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    user_id : {
      type: 'string',
      required: true,
      description: 'user who wants to subscribe'
    },
    channel: {
      type: 'json',
      required: true,
      description: 'The channel(s) to which the user wants to subscribe'
    },
    status: {
      type: 'number',
      defaultsTo: 1,
      description: '1=>subscribed,0=>unsubscribed'
    }
  },
};


