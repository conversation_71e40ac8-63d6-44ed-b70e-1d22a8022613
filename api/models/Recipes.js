/**
 * Recipe.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  primaryKey: 'rid',

  attributes: {
    rid: {
      type: 'string',
      description: 'hash',
      required: true,
      unique: true,
      maxLength: 120,
      example: 'UUID-UUID-UUID-UUID'
    },
    'siteId': {
      type: 'string',
      'description': 'global-secondary##rid',
      required: true,
      maxLength: 120
    },
    label: {
      type: 'string',
      extendedDescription: 'Description of recipe'
    },
    'isActive': {
      'type': 'string',
      defaultsTo: '0',
    },
    alwaysRun: {
      type: 'string',
    },
    'isSchedule': {
      'isIn' : ['true', 'false'],
      'type': 'string'
    },
    'neo': {
      'type': 'string',
    },
    'operator': {
      'type': 'string'
    },
    'actionable': {
      'type': 'string'
    },
    'everyMinuteTopics': {
      'type': 'string'
    },
    'params': {
      'type': 'string'
    },
    'dependentOnOthers': {
      'type': 'string',
    },
    'runOn': {
      'type': 'string',
      'description': 'global-secondary##rid',
    },
    'priority': {
      'type': 'string',
    },
    'switchOff': {
      'type': 'string',
    },
    'notRun': {
      'type': 'string',
    },
    'userId': {
      'type': 'string'
    },
    'oldObservable': {
      'type': 'string'
    },
    'scheduled': {
      'type': 'string'
    },
    'ReachedJb': {
      'type': 'string'
    },
    'schedule': {
      'type': 'string'
    },
    'rtype': {
      'type': 'string'
    },
    'isAbstract': {
      'type': 'string'
    },
    'type': {
      'type': 'string'
    },
    'recipelabel': {
      'type': 'string'
    },
    'currState': {
      'type': 'string'
    },
    'isStage': {
      'type': 'string'
    },
    'maxLogNeeded': {
      'type': 'string'
    },
    'maxDataNeeded': {
      'type': 'string'
    },
    'formula': {
      'type': 'string'
    },
    'appType': {
      'type': 'string'
    },
    'misc': {
      'type': 'string'
    },
    'componentId': {
      'type': 'string'
    },
    'user': {
      'type': 'string'
    },
    'attachments': {
      'type': 'json',
    },
    'componentsType': {
      'type': 'string'
    },
    'runInterval': {
      'type': 'string'
    },
    'isOTPanelControllingSetpoint':{
      type:'string',
      defaultsTo:'0',
      description:'this flag is responsible to control the Temperature setPoint of AHU from OT Control Panel. 1=>On, 0=>Off'
    },
    'commandRetentionRecipeStatus': {
      'type': 'string',
       'defaultsTo':'1',
       'description': '0 -> disabled and 1 -> enabled or pending'
    }
  },
};

