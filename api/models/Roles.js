/**
 * @module DyanmoKeyStore.js
 * @description :: This is simple key:value pair that holds any key value pair on site.
 */

module.exports = {
  primaryKey: 'roleName',
  'attributes': {
    'roleName': {
      'type': 'string',
      'description': 'hash'
    },
    'policies': {
      'type': 'string',
    },
    'policiesBE': {
      'type': 'string',
    },
    'defpref': {
      'type': 'string',
    },
    'isDeleted': {
      'type': 'string'
    },
  },
};
