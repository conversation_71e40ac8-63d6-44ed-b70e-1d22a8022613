module.exports = {
  datastore: "postgres",
  tableName: "sankey_graph_nodes",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    deviceId: {
      type: "string",
      columnType: "varchar(255)",
      columnName: "device_id",
    },
    deviceClass: {
      type: "string",
      columnType: "varchar(255)",
      columnName: "device_class",
    },
    deviceType: {
      type: "string",
      columnType: "varchar(255)",
      columnName: "device_type",
    },
    driverType: {
      columnName: "driver_type",
      type: "number",
    },
    level: {
      columnName: "level",
      type: "number",
    },
    graphRefId: {
      columnName: "graph_ref_id",
      type: "number",
    },
    status: {
      type: "number",
      isIn: [0, 1],
      defaultsTo: 1,
      description: "0 -> inactive, 1 -> active",
    },
    color: {
      type: "string",
    },
    offset: {
      type: "number",
    },
    createdBy: {
      type: "string",
      columnName: "created_by",
      required: true,
    },
    lastUpdatedBy: {
      type: "string",
      columnName: "last_updated_by",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
  },

  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
