/**
 * ProductionData.js
 * @description :: Production data
 */
module.exports = {
  primaryKey: "pk",
  tableName:"ShiftProductionData",
  attributes: {
    pk: {
      type: "string",
      description: "partiotn key. it will be siteId most of the time",
      example: "lmw-coi"
    },
    sk: {
      type: "string",
      description: "it will help to create composite key as well.pk and sk will make the primary of any item",
    },
    shift_name: {
      type: 'string',
      description:'shift name should be in this format '
    },
    shift_start_time: {
      type: "string",
      description:"it should be in HH:mm formart e,g for 08:00"
    },
    shift_end_time: {
      type: "string",
      description:"it should be in HH:mm formart e,g for 16:00"
    },
    product_name:{
      type:"string",
      description:"name of product eg liquid metal",
    },
    product_id:{
      type:"string",
      description:"name of product eg liquid metal",
    },
    shift_id:{
      type:"string",
      description:"name of product eg liquid metal",
    },
    unit:{
      type:"string",
      description:"tonn"
    },
    production_value: {
      type:"number",
      description:"value in unit",
      required:false,
    },
    production_unit: {
      type:"string",
      description:"unit of production"
    },
    createdAt: {
      type: "string",
      description:"Timestamp at the time of creation in YYYY-MM-HHTHH:mm:ssz format"
    },
    updatedAt: {
      type: 'string',
      description:"Timestamp at the time of updation in YYYY-MM-HHTHH:mm:ssz format"
    },
    createdBy: {
      type: "string",
      description: "last updated by e.g <EMAIL>"
    }
  },

};

