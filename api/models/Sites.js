/**
 * Sites.js
 *
 * @description :: Stores sites specific configration information
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  primaryKey: 'siteId',

  attributes: {
    siteId: {
      type: 'string',
      description: 'hash',
    },
    siteName: {
      type: 'string',
    },
    chainName: {
      type: 'string',
    },
    bussinessModel: {
      type: 'string',
    },
    industryType: {
      type: 'json',
      columnType: 'array',
    },
    perofrmancekpi: {
      type: 'json',
    },
    socketId: {
      type: 'string',
    },
    location: {
      type: 'string',
    },
    latitude: {
      type: 'string',
    },
    longitude: {
      type: 'string',
    },
    networkCount: {
      type: 'number',
    },
    regionCount: {
      type: 'number',
    },
    networks: {
      type: 'json',
    },

    regions: {
      type: 'json',
    },
    map: {
      type: 'json',
    },
    controller: {
      type: 'string',
    },
    isJouleBoxConnected: {
      type: 'number',
    },
    areas: {
      type: 'json',
    },
    planningDocUrl: {
      type: 'string',
    },
    consumptionUnit: {
      type: 'string',
    },
    circuitCount: {
      type: 'number'
    },
    unitCost: {
      type: 'number',
      required: true,
    },
    currency: {
      type: 'string',
      defaultsTo: '₹'
    },
    status: {
      type: 'number',
      defaultsTo: 1
    },
    timezone: {
      type: 'string',
      required: true,
      example: 'Asia/Kolkata',
    },
  },
};
