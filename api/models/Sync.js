/**
 * Sync.js
 *
 * @description :: Stores temp state of a controller which is to be synced
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  primaryKey: 'controllerId',

  attributes: {
    controllerId: {
      type: 'string',
      description: 'controllerId',
      required: true
    },
    type: {
      type: 'string',
      description: 'type of the info',
      required: true
    },
    switchOff: {
      type: 'string',
      description: 'state of recipe'
    }
  }

};
