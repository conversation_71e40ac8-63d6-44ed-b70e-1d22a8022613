/**
 * DataDevice.js
 *
 * @description :: This schema is for storing all the Data of device/components coming from every controller
 */

module.exports = {

  primaryKey: 'userId_siteId',

  'attributes': {

    'userId_siteId': {
      'type': 'string', //<site_id+no.of commponents> eg ssh_2 ssh_43 OR deviceId = 2771
      'description': 'hash',
      'example': 'user@smartjoules.in_mgch'
    },
    'id': {
      'type': 'string',
      'description': 'range',
      'example': '<componentId/processId>'
    },
    'config': {
      'type': 'json',
    },
  },

};

