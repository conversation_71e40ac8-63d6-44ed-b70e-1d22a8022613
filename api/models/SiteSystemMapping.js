module.exports = {
    datastore:'postgres',
    tableName: 'site_system_mapping',
    primaryKey: 'id',
    attributes: {
      id: { type: 'number', autoIncrement: true, },
      system_id: {
        type: 'number',
        // required: true,
        description: 'ID found in system table',
      },
      site_id: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'mgch',
        description: 'SiteId.',
      },
      createdAt: { type: 'ref', columnType: 'timestamp', autoCreatedAt: true, },
      updatedAt: { type: 'ref', columnType: 'timestamp', autoUpdatedAt: true, },
      svg_tag_device_map: {
        type: 'ref',
        columnType: 'json',
      },
    },
  };
