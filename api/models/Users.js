/**
 * @module Users.js
 * @description :: This is the User's for user login and his access permission.
 */

let bcrypt = require('bcryptjs');

module.exports = {
  primaryKey: 'userId',
  'schema': true,
  'attributes': {

    'userId': {
      'description': 'hash',
      'type': 'string'
    },
    'userRole': {
      'type': 'string',
      'isIn': ['admin', 'adm1n', 'smartjoules employee', 'smartjoules executive', 'site operator', 'executive']
    },

    'userOrganization': {
      'description': 'range',
      'type': 'string'
    },
    'name': {
      'type': 'string'
    },
    'address': {
      'type': 'string'
    },
    'email': {
      'required': true,
      'type': 'string'
    },
    'password': {
      'required': true,
      'type': 'string'
    },
    'phone': {
      'required': true,
      'type': 'string'
    },
    'picture': {
      'type': 'string'
    },
    'policiesGroup': {
      'type': 'string'
    },
    'defaultSite': {
      'type': 'string'
    },
    'designation': {
      'type': 'string'
    },
    'accountable': {
      'type': 'string'
    },
    'notify': {
      'type': 'string'
    },
    'isDeleted': {
      'type': 'boolean'
    },
    'passwordUpdatedAt': {
      'type': 'number'
    },
    'subscriber_id': {
      'type':'string',
      'example': '66c72253f88a5c110bcd6bb6',
      'description': 'Alert subscription id'
    }
  },

  'customToJSON': function() {
    return _.omit(this, ['password', 'createdAt', 'updatedAt']);
  },

  'beforeCreate': function(values, next) {
    bcrypt.genSalt(10, (err, salt) => {
      if (err) return next(err);
      bcrypt.hash(values.password, salt, (err, hash) => {
        if (err) return next(err);
        values.password = hash;
        next();
      });
    });
  },
  'beforeUpdate': function(values, next) {
    if (!values.password){
      next();
      return;
    }
    bcrypt.genSalt(10, (err, salt) => {
      if (err) return next(err);
      bcrypt.hash(values.password, salt, (err, hash) => {
        if (err) return next(err);
        values.password = hash;
        next();
      });
    });
  },
};
