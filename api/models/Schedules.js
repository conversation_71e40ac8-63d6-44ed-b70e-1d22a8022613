/**
 * Schedules.js
 *
 * @description :: Stores all the automation schedules (recipe/pid etc)
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  primaryKey: 'sid',

  attributes: {
    'sid': {
      type: 'string',
      description: 'hash',
      required: true,
      unique: true,
      maxLength: 120,
      example: 'UUID-UUID-UUID-UUID'
    },
    'rid': {
      type: 'string',
      description: 'range',
      required: true,
      unique: true,
      maxLength: 120,
      example: 'UUID-UUID-UUID-UUID'
    },
    'siteId': {
      type: 'string',
      required: true,
      maxLength: 120
    },
    'runOn': {
      'type': 'string'
    },
    'ts': {
      'type': 'string',
    },
    'schedule': {
      'type': 'string',
    },
    'reachedJB': {
      'type': 'string',
    },
    'repeat_type': {
      'type': 'string',
    },
    'isDeployed': {
      'type': 'string'
    }
  }
};
