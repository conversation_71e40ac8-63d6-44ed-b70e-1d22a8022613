import PostgresConnection from '../connections/postgres.js';
import AlertTemplateRepository from './AlertTemplate.repository.js';
import AlertInventoryRepository from './AlertInventory.repository.js';

export async function getAlertInventoryByEventId({ alertEventId, siteId, assetId }) {
  const alertTemplateRecord = await AlertTemplateRepository.fetchAlertTemplateByEventId(alertEventId);
  if (!alertTemplateRecord) {
    return null;
  }
  const { id: alertTemplateId, template_category: ALERT_TEMPLATE_CATEGORY } = alertTemplateRecord;
  let alertInventoryRecord;
  if (ALERT_TEMPLATE_CATEGORY === 1) {
    alertInventoryRecord = await AlertInventoryRepository.findGlobalAlertInventoryById({
      alertTemplateId,
      siteId,
      asset_id: assetId,
    });
  } else if (ALERT_TEMPLATE_CATEGORY === 2) {
    alertInventoryRecord = await AlertInventoryRepository.findLocalAlertInventoryById({
      alertTemplateId,
    });
  }

  if (alertTemplateRecord && !alertInventoryRecord && ALERT_TEMPLATE_CATEGORY === 1) {
    /**
     * Registering the Global Alert for siteId and assetId if not available
     */
    const alertInventoryPayload = {
      alert_template_ref_id: alertTemplateRecord.id,
      name: alertTemplateRecord.name,
      description: alertTemplateRecord.description,
      siteid: siteId,
      severity: alertTemplateRecord.severity,
      asset_id: assetId,
      created_by: 'auto',
    };
    alertInventoryRecord = await AlertInventoryRepository.create(alertInventoryPayload);
  }
  if (!alertInventoryRecord) {
    return null;
  }
  const alertMetaData = {};
  alertMetaData.alertInventoryId = alertInventoryRecord.id;
  alertMetaData.alertTemplateId = alertTemplateRecord.id;
  alertMetaData.severity = alertInventoryRecord.severity;
  alertMetaData.alertName = alertInventoryRecord.name;
  alertMetaData.alertDescription = alertInventoryRecord.description;
  alertMetaData.STATEFUL_ALERT_FLAG = 1; //TODO: this flag will
  alertMetaData.ALERT_TEMPLATE_CATEGORY = alertTemplateRecord.template_category;
  alertMetaData.ALERT_CATEGORY = alertTemplateRecord.alert_category;
  alertMetaData.alertEventId = alertTemplateRecord.observer_execution_ref_id;
  alertMetaData.siteId = alertInventoryRecord.siteid;
  alertMetaData.assetId = alertInventoryRecord.asset_id;
  return alertMetaData;
}

export async function fetchPreviousAlertStateFromDatabase(alertInventoryId, pgCon = null) {
  const query = `
    SELECT
      id as incident_id,
      CASE
        WHEN issue_resolved_at IS NULL THEN 'OCCURRED'
        ELSE 'RESOLVED'
      END as event_name,
      CASE
        WHEN issue_resolved_at IS NULL THEN issue_occurred_at
        ELSE issue_resolved_at
      END as timestamp,
      block_id
    FROM alert_incident_history
    WHERE alert_inventory_id = $1
    ORDER BY issue_occurred_at DESC
    LIMIT 1
  `;
  const values = [alertInventoryId];

  let result;
  if (pgCon) {
    result = await pgCon.execute(query, values);
  } else {
    const dbCon = new PostgresConnection();
    result = await dbCon.query(query, values);
  }

  return result.rows[0];
}
