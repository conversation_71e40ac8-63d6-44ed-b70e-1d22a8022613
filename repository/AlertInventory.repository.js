// noinspection JSCheckFunctionSignatures

import AlertInventory from '../models/AlertInventory.js';
import redisClient from '../connections/redis.js';
import { isEmpty } from '../helpers/index.js';
import logger from '../utils/logger.js';

export default class AlertInventoryRepository {
  static async create(param) {
    const alertInventoryKey = `ALERT_TEMPLATE:${param.alertTemplateId}:Alert_Inventory:siteId:${param.siteId}:asset_id:${param.asset_id}`;
    const InventoryDetailFromCache = await redisClient.get(alertInventoryKey);
    // TODO: to allow asset_type update
    // if (InventoryDetailFromCache) {
    //   return JSON.parse(InventoryDetailFromCache);
    // }

    const incidentAlertDetail = await AlertInventory.findOne({
      where: {
        alert_template_ref_id: param.alert_template_ref_id,
        siteid: param.siteid,
        asset_id: param.asset_id,
      },
    });

    if (incidentAlertDetail) {
      const alertDetailJSON = incidentAlertDetail.toJSON();

      if (!alertDetailJSON.asset_type) {
        const asset_type = await AlertInventoryRepository.getDeviceTypeFromAssetId(param.asset_id);
        if (asset_type) {
          try {
            await AlertInventory.update(
              { asset_type },
              { where: { id: alertDetailJSON.id } }
            );
            alertDetailJSON.asset_type = asset_type;
            logger.info('Successfully updated alert inventory with asset_type', {
              inventoryId: alertDetailJSON.id,
              assetType: asset_type
            });
          } catch (error) {
            logger.error('Failed to update asset_type', { id: alertDetailJSON.id, error: error.message });
          }
        }
      }

      await AlertInventoryRepository._storeGlobalAlertInventoryInCache(alertDetailJSON);
      return alertDetailJSON;
    }

    let createPayload = {
      alert_template_ref_id: param.alert_template_ref_id,
      name: param.name,
      description: param.description,
      siteid: param.siteid,
      severity: param.severity,
      asset_id: param.asset_id,
      created_by: 'auto',
    };

    const asset_type = await AlertInventoryRepository.getDeviceTypeFromAssetId(param.asset_id);
    if (asset_type) {
      createPayload.asset_type = asset_type;
    }

    const _newAlertInventoryRecord = await AlertInventory.create(createPayload);
    await AlertInventoryRepository._storeGlobalAlertInventoryInCache(_newAlertInventoryRecord.toJSON());
    return _newAlertInventoryRecord.toJSON();
  }

  static async getDeviceTypeFromAssetId(assetId) {
    try {
      const componentConfigKey = `componentConfig:componentId:${assetId}`;
      const componentConfigData = await redisClient.get(componentConfigKey);

      logger.info('👀componentConfigData', { componentConfigData });
      if (!isEmpty(componentConfigData)) {
        const parsed = JSON.parse(componentConfigData);
        if (parsed && parsed.deviceType) {
          return parsed.deviceType;
        }
      }

      return null;
    } catch (error) {
      logger.error('Error retrieving device type from Redis', { assetId, error: error.message });
      return null;
    }
  }

  static async findGlobalAlertInventoryById({ alertTemplateId, siteId, asset_id }) {
    let alertInventoryRecord;
    const alertInventoryKey = `ALERT_TEMPLATE:${alertTemplateId}:Alert_Inventory:siteId:${siteId}:asset_id:${asset_id}`;
    alertInventoryRecord = await redisClient.get(alertInventoryKey);
    // TODO: to allow asset_type update
    // if (!isEmpty(alertInventoryRecord)) return JSON.parse(alertInventoryRecord);
    alertInventoryRecord = await AlertInventory.findOne({
      where: {
        alert_template_ref_id: alertTemplateId,
        siteid: siteId,
        asset_id,
      },
    });
    if (!alertInventoryRecord) return null;

    const alertInventoryJSON = alertInventoryRecord.toJSON();

    // Check if asset_type is missing and try to update it
    if (!alertInventoryJSON.asset_type) {
      const asset_type = await AlertInventoryRepository.getDeviceTypeFromAssetId(asset_id);
      if (asset_type) {
        try {
          await AlertInventory.update(
            { asset_type },
            { where: { id: alertInventoryJSON.id } }
          );
          alertInventoryJSON.asset_type = asset_type;
          logger.info('Successfully updated existing alert inventory with asset_type', {
            inventoryId: alertInventoryJSON.id,
            assetType: asset_type
          });
        } catch (error) {
          logger.error('Failed to update asset_type for existing record', {
            inventoryId: alertInventoryJSON.id,
            error: error.message
          });
        }
      }
    }

    await AlertInventoryRepository._storeGlobalAlertInventoryInCache(alertInventoryJSON);
    return alertInventoryJSON;
  }

  static async findLocalAlertInventoryById({ alertTemplateId }) {
    let alertInventoryRecord;
    const alertInventoryKey = `ALERT_TEMPLATE:${alertTemplateId}:Alert_Inventory`;
    alertInventoryRecord = await redisClient.get(alertInventoryKey);
    // TODO: to allow asset_type update
    // if (!isEmpty(alertInventoryRecord)) return JSON.parse(alertInventoryRecord);

    alertInventoryRecord = await AlertInventory.findOne({
      where: {
        alert_template_ref_id: alertTemplateId,
      },
    });
    if (!alertInventoryRecord) return null;

    const alertInventoryJSON = alertInventoryRecord.toJSON();

    // For local alerts, also check if asset_type is missing when asset_id is present
    if (!alertInventoryJSON.asset_type && alertInventoryJSON.asset_id) {
      const asset_type = await AlertInventoryRepository.getDeviceTypeFromAssetId(alertInventoryJSON.asset_id);
      if (asset_type) {
        try {
          await AlertInventory.update(
            { asset_type },
            { where: { id: alertInventoryJSON.id } }
          );
          alertInventoryJSON.asset_type = asset_type;
          logger.info('Successfully updated local alert inventory with asset_type', {
            inventoryId: alertInventoryJSON.id,
            assetType: asset_type
          });
        } catch (error) {
          logger.error('Failed to update asset_type for local alert', {
            id: alertInventoryJSON.id,
            error: error.message
          });
        }
      }
    }

    await redisClient.set(alertInventoryKey, JSON.stringify(alertInventoryJSON));
    await redisClient.expire(alertInventoryKey, 24 * 7 * 60 * 60); // 7d
    return alertInventoryJSON;
  }

  static async _storeGlobalAlertInventoryInCache(inventoryData) {
    const alertInventoryKey = `ALERT_TEMPLATE:${inventoryData.alert_template_ref_id}:Alert_Inventory:siteId:${inventoryData.siteid}:asset_id:${inventoryData.asset_id}`;
    await redisClient.set(alertInventoryKey, JSON.stringify(inventoryData));
    await redisClient.expire(alertInventoryKey, 24 * 7 * 60 * 60); // 7d
  }
}
