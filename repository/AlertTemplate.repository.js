// noinspection JSCheckFunctionSignatures

import redisClient from '../connections/redis.js';
import { isEmpty } from '../helpers/index.js';
import AlertTemplate from '../models/AlertTemplate.js';

export default class AlertTemplateRepository {
  static async fetchAlertTemplateByEventId(eventId) {
    let alertTemplateData;
    const alertTemplateCacheKey = `ALERT_TEMPLATE:${eventId}`;
    alertTemplateData = await redisClient.get(alertTemplateCacheKey);
    if (!isEmpty(alertTemplateData)) return JSON.parse(alertTemplateData);
    alertTemplateData = await AlertTemplate.findOne({
      where: {
        observer_execution_ref_id: eventId,
        status: 1,
      },
    });
    if (isEmpty(alertTemplateData)) return null;
    await redisClient.set(alertTemplateCacheKey, JSON.stringify(alertTemplateData.toJSON()));
    await redisClient.expire(alertTemplate<PERSON>ache<PERSON><PERSON>, 24 * 7 * 60 * 60);
    return alertTemplateData;
  }
}
