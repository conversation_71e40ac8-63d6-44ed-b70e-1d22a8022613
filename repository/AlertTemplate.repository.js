// noinspection JSCheckFunctionSignatures

import redisClient from '../connections/redis.js';
import { isEmpty } from '../helpers/index.js';
import AlertTemplate from '../models/AlertTemplate.js';

export default class AlertTemplateRepository {
  static async fetchAlertTemplateByEventId(eventId) {
    let alertTemplateData;
    const alertTemplateCacheKey = `ALERT_TEMPLATE:${eventId}`;
    alertTemplateData = await redisClient.get(alertTemplateCacheKey);
    if (!isEmpty(alertTemplateData)) return JSON.parse(alertTemplateData);
    alertTemplateData = await AlertTemplate.findOne({
      where: {
        observer_execution_ref_id: eventId,
        status: { '!=': 0 }, // Exclude only Deleted (0) templates
      },
    });
    if (isEmpty(alertTemplateData)) return null;
    await redisClient.set(alertTemplateCacheKey, JSON.stringify(alertTemplateData.toJSON()));
    await redisClient.expire(alertTemplateCacheKey, 24 * 7 * 60 * 60);
    return alertTemplateData;
  }

  static async invalidateAlertTemplateCache(eventId) {
    const alertTemplateCacheKey = `ALERT_TEMPLATE:${eventId}`;
    await redisClient.del(alertTemplateCacheKey);
  }
}
