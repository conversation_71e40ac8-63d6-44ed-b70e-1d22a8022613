import { InfluxDB, Point } from '@influxdata/influxdb-client';
import moment from 'moment-timezone';

let writeApi = null;

const INFLUX_CONFIG = {
  url: process.env.INFLUX_OSS_IOT_METRICS_URL,
  token: process.env.INFLUX_OSS_IOT_METRICS_TOKEN,
  org: process.env.INFLUX_OSS_IOT_METRICS_ORG,
  bucket: process.env.INFLUX_OSS_IOT_METRICS_BUCKET || 'smart_alert_eval',
};

function getOrCreateWriteApi() {
  if (writeApi) return writeApi;

  writeApi = new InfluxDB({
    url: INFLUX_CONFIG.url,
    token: INFLUX_CONFIG.token,
  }).getWriteApi(INFLUX_CONFIG.org, INFLUX_CONFIG.bucket, 's');

  return writeApi;
}

/**
 * Gracefully closes the InfluxDB connection
 */
export async function flushInflux() {
  if (!writeApi) return;
  try {
    await writeApi.close();
    writeApi = null;
  } catch (error) {
    throw new Error(`[INFLUX] Error closing connection: ${error.message}`);
  }
}

export async function ingestIncidentToInflux({
  siteId,
  alertInventoryId,
  incidentId,
  observer_execution_ref_id,
  timestamp,
  result,
}) {
  try {
    const timestampInSeconds = moment.tz(timestamp, 'UTC').unix();

    const point = new Point('smartalerteval_new')
      .timestamp(timestampInSeconds)
      .tag('siteid', String(siteId))
      .tag('alert_inventory_id', String(alertInventoryId))
      .tag('recipeid', String(observer_execution_ref_id))
      .tag('alert_incident_id', String(incidentId))
      .intField('result', result);

    const api = getOrCreateWriteApi();
    api.writePoint(point);
  } catch (error) {
    throw new Error(`[INFLUX] Error writing incident point: ${error.message}`);
  }
}
