export async function checkActiveIncident({ alert_inventory_id, pgCon }) {
  const query = `
    SELECT id, issue_occurred_at, block_id
    FROM alert_incident_history
    WHERE alert_inventory_id = $1
    AND issue_resolved_at IS NULL
    ORDER BY issue_occurred_at DESC
    LIMIT 1
    FOR UPDATE`;
  const values = [alert_inventory_id];
  const result = await pgCon.execute(query, values);
  return result.rows[0];
}

export async function createAlertIncident({ alert_inventory_id, issue_occurred_at, block_id, pgCon }) {
  const query = `
    INSERT INTO alert_incident_history (
      alert_inventory_id,
      issue_occurred_at,
      last_notification_triggerred_at,
      occurred_event_count,
      recent_occurred_event_ts,
      block_id
    )
    VALUES ($1, $2, $2, 1, $2, $3)
    RETURNING id
  `;
  const values = [alert_inventory_id, issue_occurred_at, block_id];
  try {
    const result = await pgCon.execute(query, values);
    const { rows } = result;
    if (rows.length === 0) throw new Error('Failed to create alert incident');
    return rows[0].id;
  } catch (error) {
    throw error;
  }
}

export async function updateIncidentBlockId({ id, block_id, pgCon }) {
  const query = `
    UPDATE alert_incident_history
    SET block_id = $1
    WHERE id = $2
    AND issue_resolved_at IS NULL
    RETURNING id
  `;
  const values = [block_id, id];
  try {
    const result = await pgCon.execute(query, values);
    if (result.rows.length === 0) {
      throw new Error(`No active incident found with ID ${id}`);
    }
    return result.rows[0];
  } catch (error) {
    throw error;
  }
}

export async function lockIncidentById({ id, pgCon }) {
  const query = `
    SELECT id, issue_occurred_at, block_id
    FROM alert_incident_history
    WHERE id = $1
    AND issue_resolved_at IS NULL
    FOR UPDATE
  `;
  const values = [id];
  const result = await pgCon.execute(query, values);
  return result.rows[0];
}

export async function resolveAlertIncident({
  id,
  issue_resolved_at,
  occurred_event_count,
  recent_occurred_event_ts,
  block_id,
  pgCon,
}) {
  const query = `
    UPDATE alert_incident_history
    SET
      issue_resolved_at = $1,
      last_notification_triggerred_at = $1,
      occurred_event_count = $2,
      recent_occurred_event_ts = COALESCE($3, recent_occurred_event_ts),
      block_id = COALESCE($4, block_id)
    WHERE id = $5
  `;
  const values = [issue_resolved_at, occurred_event_count, recent_occurred_event_ts, block_id, id];
  await pgCon.execute(query, values);
}
