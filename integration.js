import 'dotenv/config';

import logger from './utils/logger.js';
import PostgresConnection from './connections/postgres.js';
import { createAlertIncident, lockIncidentById, resolveAlertIncident } from './repository/AlertIncident.repository.js';
import { fetchPreviousAlertStateFromDatabase } from './repository/Alert.repository.js';


const getRandomAlertInventoryId = () => Math.floor(Math.random() * 1000) + 1;

const testTransactionFeature = async () => {

  const alertInventoryId = getRandomAlertInventoryId();
  const siteId = 'test-site';
  const timestamp = new Date().toISOString();

  logger.info('🚀 new PostgresConnection', new PostgresConnection());
  try {
    const pgCon = await PostgresConnection.beginTransaction();
    try {
      const incidentId = await createAlertIncident({
        alert_inventory_id: alertInventoryId,
        issue_occurred_at: timestamp,
        pgCon,
      });

      logger.info(`Alert incident created with ID: ${incidentId}`);

      const lockedIncident = await lockIncidentById({
        id: incidentId,
        pgCon,
      });

      if (!lockedIncident) {
        throw new Error('Failed to lock incident - this should not happen!');
      }

      logger.info('Incident locked successfully');

      await resolveAlertIncident({
        id: incidentId,
        issue_resolved_at: timestamp,
        occurred_event_count: 1,
        recent_occurred_event_ts: timestamp,
        pgCon,
      });

      logger.info('Incident resolved successfully');

      await pgCon.commit();

      logger.info('Verifying the incident after commit...');
      const verifiedIncident = await fetchPreviousAlertStateFromDatabase(alertInventoryId);

      if (!verifiedIncident) {
        throw new Error('Incident not found after transaction commit!');
      }

      logger.info('Transaction test successful!', {
        incidentId: verifiedIncident.incident_id,
        eventName: verifiedIncident.event_name,
        timestamp: verifiedIncident.timestamp
      });

    } catch (error) {
      logger.error('Error in transaction test, rolling back:', error);
      await pgCon.rollback();
      throw error;
    }
  } catch (error) {
    logger.error('Transaction test failed:', error);
  }
};

// Run the test
(async function runTests() {
  try {
    await testTransactionFeature();
    logger.info('Integration tests completed');
    setTimeout(() => process.exit(0), 1000);
  } catch (error) {
    logger.error('Integration tests failed:', error);
    process.exit(1);
  }
})();
