import redisClient from '../connections/redis.js';
import { redisConfig } from '../config/index.js';
import logger from './logger.js';

/**
 * Executes an operation with a distributed lock
 * @param {string} lockKey - The key to use for the lock
 * @param {Function} operation - The async operation to execute while holding the lock
 * @returns {Promise<any>} - The result of the operation, or null if lock couldn't be acquired
 */
export async function withLock(lockKey, operation) {
  try {
    const acquired = await redisClient.set(lockKey, '1', {
      PX: redisConfig.LOCK_TTL,
      NX: true
    });

    if (acquired !== 'OK') {
      logger.info(`Lock already held, skipping operation`, { lockKey });
      return null;
    }

    try {
      return await operation();
    } finally {
      await redisClient.del(lockKey);
    }
  } catch (error) {
    logger.error(`Error in lock operation: ${error.message}`, { lockKey, error });
    throw error;
  }
}
