import logger from './logger';

/**
 * Attempts to parse a JSON string into a JavaScript value
 * @param value - The string to parse
 * @returns The parsed value or null if parsing fails
 */
export function parseMessage(value: string): unknown | null {
  try {
    return JSON.parse(value);
  } catch (error) {
    logger.error('Failed to parse message', {
      error: error instanceof Error ? error.message : String(error),
    });
    return null;
  }
}
