import RedisConnection from '../connection/redis';
import logger from './logger';

export const CACHE_TTL = {
  SUBSCRIBERS: 5 * 60, // 5m
  TEMPLATES: 15 * 60, // 15m
  USER_DETAILS: 30 * 60, // 30m
  SITE_DETAILS: 60 * 60, // 1h
  ASSET_DETAILS: 60 * 60, // 1h
  NOTIFICATION_PREFERENCES: 5 * 60, // 5m
  RECIPE_BLOCK: 60 * 60, // 1h
} as const;

export class CacheService {
  private static instance: CacheService;
  private readonly redis = RedisConnection.getInstance();

  private constructor() {}

  public static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  async getFromCacheOrDB<T>(key: string, fetchFn: () => Promise<T>, ttl: number): Promise<T> {
    try {
      const cached = await this.redis.get(key);
      if (cached) return JSON.parse(cached);
    } catch (error) {
      logger.warn(`Cache operation failed: ${error.message}`);
    }

    const data = await fetchFn();

    try {
      await this.redis.set(key, JSON.stringify(data), ttl);
    } catch (error) {
      logger.warn(`Cache operation failed: ${error.message}`);
    }

    return data;
  }
}
