import { createLogger, format, transports } from 'winston';
import * as Sen<PERSON> from '@sentry/node';
import SentryTransport from 'winston-transport-sentry-node';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
});

const logger = createLogger({
  format: format.json(),
  transports: [
    new transports.Console({
      level: 'info',
    }),
    new SentryTransport({
      sentry: { dsn: process.env.SENTRY_DSN },
      level: 'error',
    }),
  ],
  exitOnError: false,
});

export default logger;
