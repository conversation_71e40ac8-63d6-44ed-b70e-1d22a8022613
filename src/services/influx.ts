import { InfluxDB, Point } from '@influxdata/influxdb-client';
import moment from 'moment-timezone';
import { influxConfig } from '../config/influx';
import logger from '../utils/logger';

let writeApi: any = null;

function getOrCreateWriteApi() {
  if (writeApi) return writeApi;

  const { url, token, org, bucket } = influxConfig;

  writeApi = new InfluxDB({
    url,
    token,
  }).getWriteApi(org, bucket, 's');

  return writeApi;
}

export async function flushInflux(): Promise<void> {
  if (!writeApi) return;
  try {
    await writeApi.close();
    writeApi = null;
  } catch (error) {
    logger.error('Failed to close InfluxDB connection', {
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

export async function ingestNotificationToInflux({
  siteId,
  alertInventoryId,
  incidentId,
  observer_execution_ref_id,
  channel,
  timestamp,
  result = 1,
}: {
  siteId: string;
  alertInventoryId: number;
  incidentId: number;
  observer_execution_ref_id: string;
  channel: string;
  timestamp: string;
  result?: number;
}): Promise<void> {
  try {
    const timestampInSeconds = moment.tz(timestamp, 'UTC').unix();

    const point = new Point('notification_processing')
      .timestamp(timestampInSeconds)
      .tag('incident_id', String(incidentId))
      .tag('alert_inventory_id', String(alertInventoryId))
      .tag('observer_execution_ref_id', String(observer_execution_ref_id))
      .tag('site_id', String(siteId))
      .tag('channel', channel)
      .intField('result', result);

    const api = getOrCreateWriteApi();
    api.writePoint(point);
  } catch (error) {
    logger.error('Failed to ingest metrics', {
      error: error instanceof Error ? error.message : String(error),
      siteId,
      alertInventoryId,
    });
  }
}
