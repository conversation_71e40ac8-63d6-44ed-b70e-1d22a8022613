import { v4 as uuidv4 } from 'uuid';
import moment from 'moment-timezone';
import alertRepository from '../repositories/alert-repository.js';
import logger from '../utils/logger.js';
import { withLock } from '../utils/lock.js';
import { kafkaConfig } from '../config/index.js';
import kafkaConnection from '../connections/kafka.js';

class AlertService {
  /**
   * Find and process alerts that need reminders
   */
  async processReminders() {
    try {
      const pendingReminders = await alertRepository.findPendingReminders();

      if (pendingReminders.length === 0) {
        return [];
      }

      const processPromises = pendingReminders.map(reminder => this.processSingleReminder(reminder));
      const results = await Promise.allSettled(processPromises);

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      logger.info(`Reminder processing complete: ${successful} succeeded, ${failed} failed`);

      return results
        .filter(r => r.status === 'fulfilled')
        .map(r => r.value)
        .filter(Boolean);
    } catch (error) {
      logger.error(`Failed to process reminders: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process a single reminder with locking to prevent duplicates
   * @param {Object} reminder - The reminder to process
   * @returns {Promise<Object|null>} - The notification payload or null if not processed
   */
  async processSingleReminder(reminder) {
    const lockKey = `reminder:lock:${reminder.id}`;

    return withLock(lockKey, async () => {
      try {
        // Update the last notification time
        await alertRepository.updateLastNotificationTime(reminder.id);

        // Send notification to Kafka
        await this.sendReminderNotification(reminder);

        return {
          incidentId: reminder.id,
          alertInventoryId: reminder.alert_inventory_id,
          success: true
        };
      } catch (error) {
        logger.error(`Failed to process reminder: ${error.message}`);
        return null;
      }
    });
  }

  /**
   * Send a reminder notification for an alert
   * @param {Object} reminder - The reminder to send
   */
  async sendReminderNotification(reminder) {
    const transactionId = `reminder_${uuidv4()}`;
    const timestamp = moment().tz('UTC').toISOString();

    const notificationPayload = {
      incidentId: reminder.id,
      alertInventoryId: reminder.alert_inventory_id,
      eventName: 'REMINDER',
      assetId: reminder.asset_id,
      siteId: reminder.siteid,
      severity: reminder.severity,
      alertTemplateId: reminder.alert_template_ref_id,
      ALERT_CATEGORY: reminder.alert_category,
      observer_execution_ref_id: reminder.observer_execution_ref_id,
      timestamp,
      timestampOccurred: moment(reminder.issue_occurred_at).tz('UTC').toISOString(),
      transactionId
    };

    try {
      await kafkaConnection.sendMessage(
        kafkaConfig.NOTIFICATION_QUEUE_TOPIC,
        notificationPayload
      );

      logger.info('🚀 Reminder notification sent', notificationPayload);
    } catch (error) {
      logger.error(`Failed to send reminder notification: ${error.message}`, notificationPayload);
      throw error;
    }
  }
}

export default new AlertService();
