import { z } from 'zod';

export const NotificationPayloadSchema = z.object({
  incidentId: z.number(),
  alertInventoryId: z.number(),
  eventName: z.enum(['OCCURRED', 'RESOLVED', 'REMINDER']),
  assetId: z.string(),
  siteId: z.string(),
  severity: z.string(),
  alertTemplateId: z.number(),
  ALERT_CATEGORY: z.string(),
  observer_execution_ref_id: z.string(),
  timestamp: z.string(),
  timestampOccurred: z.string().min(0).nullable().optional(),
  transactionId: z.string(),
});

export type NotificationPayload = z.infer<typeof NotificationPayloadSchema>;
