/**
 * @description Represents the structure for a notification message for each channel.
 */
export interface ChannelNotificationMessage {
  recipients: any[];
  content: {
    title: string;
    body: string;
  };
  channel: string;
  metadata: {
    incidentId: number | string;
    alertInventoryId: number | string;
    eventName: string;
    deviceId: string;
    deviceType?: string;
    siteId: string;
    siteName?: string;
    severity: string;
    alertType: string;
    observer_execution_ref_id: string;
    templateId?: string | null;
    timestamp: string | null;
    timestampOccurred?: string | null;
  };
  ts: string;
  transactionId: string;
}
