import alertService from '../services/alert-service.js';
import logger from '../utils/logger.js';

/**
 * Runs the alert reminder process once
 * For use with Kubernetes CronJob
 */
export async function runReminderProcess() {
  try {
    logger.info('Starting alert reminder process');
    const results = await alertService.processReminders();
    logger.info(`Alert reminder process completed with ${results.length} reminders processed`);
    return results;
  } catch (error) {
    logger.error(`Alert reminder process failed: ${error.message}`);
    throw error;
  }
}
