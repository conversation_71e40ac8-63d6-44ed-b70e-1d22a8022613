import pg from 'pg';
import { postgresConfig } from '../config/index.js';
import logger from '../utils/logger.js';

const { Pool } = pg;

class PostgresConnection {
  constructor() {
    if (PostgresConnection.instance) {
      return PostgresConnection.instance;
    }
    this.pool = new Pool({
      user: postgresConfig.DB_USER,
      host: postgresConfig.DB_HOST,
      database: postgresConfig.DB_NAME,
      password: postgresConfig.DB_PASSWORD,
      port: postgresConfig.DB_PORT,
    });

    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle PostgreSQL client', err);
    });

    PostgresConnection.instance = this;
  }

  async query(sql, params) {
    const client = await this.pool.connect();
    try {
      return await client.query(sql, params);
    } finally {
      client.release();
    }
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
    }
  }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  try {
    if (PostgresConnection.instance) {
      await PostgresConnection.instance.close();
      logger.info('PostgreSQL pool has been closed');
    }
  } catch (error) {
    logger.error(`Error closing PostgreSQL pool: ${error.message}`, error);
  }
});

export default PostgresConnection;
