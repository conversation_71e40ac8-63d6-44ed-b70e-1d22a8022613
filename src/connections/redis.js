import { createClient } from 'redis';
import redisConfig from '../config/redis.js';
import logger from '../utils/logger.js';

const redisClient = createClient({
  url: `${redisConfig.REDIS_PROTOCOL}://${redisConfig.REDIS_HOST}:${redisConfig.REDIS_PORT}`,
});

redisClient.on('error', (error) => {
  logger.error(`Redis connection error: ${error.message}`, error);
});

redisClient.on('connect', () => {
  logger.info('Redis connected', { host: redisConfig.REDIS_HOST });
});

// Connect to Redis
(async () => {
  try {
    await redisClient.connect();
  } catch (error) {
    logger.error(`Failed to connect to Redis: ${error.message}`, error);
  }
})();

export default redisClient;
