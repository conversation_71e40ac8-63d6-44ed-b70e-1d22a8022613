import recipeDb from '../connection/recipe-postgres';
import postgres from '../connection/postgres';
import logger from '../utils/logger';
import { CacheService, CACHE_TTL } from '../utils/cache';

interface ChildRecipe {
  id: number;
  uniqid: string;
  description: string;
  block_type: string;
  parent_title: string;
  parent_description: string;
  parent_recipe_id: number;
}

interface BlockIdRecord {
  block_id: string | null;
}

export class RecipeRepository {
  private readonly cache = CacheService.getInstance();

  /**
   * Fetches child recipe details by block ID
   */
  async getChildRecipeByBlockId(blockId: string): Promise<ChildRecipe | null> {
    if (!blockId) return null;

    const cacheKey = `alert:recipe:block:${blockId}`;

    return this.cache.getFromCacheOrDB(
      cacheKey,
      async () => {
        try {
          const query = `
            SELECT
              cr.id,
              cr.uniqid,
              cr.description,
              cr.block_type,
              ri.title as parent_title,
              ri.description as parent_description,
              ri.id as parent_recipe_id
            FROM
              children_recipes cr
            JOIN
              recipe_info ri ON cr.parent_recipe_id = ri.id
            WHERE
              cr.uniqid = $1
          `;

          const result = await recipeDb.query<ChildRecipe>(query, [blockId]);

          if (result.rows.length === 0) {
            logger.warn(`No child recipe found for blockId: ${blockId}`);
            return null;
          }

          return result.rows[0];
        } catch (error) {
          logger.error('Failed to fetch child recipe', { blockId, error: error.message });
          return null;
        }
      },
      CACHE_TTL.RECIPE_BLOCK
    );
  }

  /**
   * Fetches block ID for a given incident ID from the alert database
   */
  async getBlockIdForIncidentId(incidentId: number): Promise<string | null> {
    if (!incidentId) return null;

    try {
      const query = `
        SELECT block_id
        FROM alert_incident_history
        WHERE id = $1
      `;

      const result = await postgres.query<BlockIdRecord>(query, [incidentId]);

      if (result.rows.length === 0) {
        logger.debug(`No record found for incident ID: ${incidentId}`);
        return null;
      }

      const blockId = result.rows[0]?.block_id;
      if (!blockId) {
        logger.debug(`Block ID is null for incident ID: ${incidentId}`);
        return null;
      }

      return blockId;
    } catch (error) {
      logger.error('Failed to fetch block_id for incident', { incidentId, error: error.message });
      return null;
    }
  }
}

export default new RecipeRepository();
