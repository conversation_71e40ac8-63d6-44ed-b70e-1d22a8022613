import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand, QueryCommandInput } from '@aws-sdk/lib-dynamodb';
import { awsConfig } from '../config';
import logger from '../utils/logger';

export class DynamoDB {
  private readonly docClient: DynamoDBDocumentClient;

  constructor() {
    this.docClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: awsConfig.region }));
  }

  private async query(
    tableName: string,
    keyConditionExpression: string,
    expressionAttributeValues: Record<string, any>,
  ): Promise<any[]> {
    try {
      const params: QueryCommandInput = {
        TableName: tableName,
        KeyConditionExpression: keyConditionExpression,
        ExpressionAttributeValues: expressionAttributeValues,
      };

      const result = await this.docClient.send(new QueryCommand(params));
      return result.Items || [];
    } catch (error) {
      const errorMsg = `Failed to query DynamoDB table=${tableName}: ${error instanceof Error ? error.message : error}`;
      error.message = errorMsg;
      throw error;
    }
  }

  async getUserDetailById(userId: string): Promise<any[]> {
    return this.query('users', 'userId = :id', { ':id': userId });
  }

  async getAssetDetails(assetId: string): Promise<{ name: string; type: string }> {
    const isDeviceId = /^\d+$/.test(assetId);
    const tableName = isDeviceId ? 'devices' : 'components';

    try {
      const results = await this.query(tableName, 'deviceId = :id', { ':id': assetId });
      const asset = results?.[0];
      return {
        name: asset?.name || '',
        type: asset?.deviceType || '',
      };
    } catch (error) {
      const errorMsg = `Failed to fetch asset details for assetId=${assetId} from table=${tableName}: ${
        error instanceof Error ? error.message : error
      }`;
      error.message = errorMsg;
      throw error;
    }
  }

  async getSiteName(siteId: string): Promise<string> {
    try {
      const results = await this.query('sites', 'siteId = :id', { ':id': siteId });
      return results?.[0]?.siteName || siteId;
    } catch (error) {
      const errorMsg = `Failed to fetch site name for siteId=${siteId}: ${
        error instanceof Error ? error.message : error
      }`;
      error.message = errorMsg;
      throw error;
    }
  }

  async getUserNotificationPreferences(userId: string, siteId: string): Promise<Record<string, any>> {
    try {
      const results = await this.query('usersitemaps', 'userId = :userId AND siteId = :siteId', {
        ':userId': userId,
        ':siteId': siteId,
      });

      const userSiteMap = results?.[0];

      if (!userSiteMap) {
        return {
          email: 1,
          sms: 1,
          whatsapp: 1,
        };
      }

      const preferences: Record<string, number> = {
        email: 1,
        sms: 1,
        whatsapp: 1,
      };

      if (userSiteMap.mailConfig) {
        try {
          const mailConfig = JSON.parse(userSiteMap.mailConfig);
          if (mailConfig?.JouleRecipe == 0) {
            preferences.email = 0;
          }
        } catch (error) {
          logger.error('Failed to parse mailConfig', { userId, siteId, error });
        }
      }

      if (userSiteMap.msgConfig) {
        try {
          const msgConfig = JSON.parse(userSiteMap.msgConfig);
          if (msgConfig?.JouleRecipe == 0) {
            preferences.sms = 0;
          }
        } catch (error) {
          logger.error('Failed to parse msgConfig', { userId, siteId, error });
        }
      }

      if (userSiteMap.whatsappConfig) {
        try {
          const whatsappConfig = JSON.parse(userSiteMap.whatsappConfig);
          if (whatsappConfig?.JouleRecipe == 0) {
            preferences.whatsapp = 0;
          }
        } catch (error) {
          logger.error('Failed to parse whatsappConfig', { userId, siteId, error });
        }
      }

      return preferences;
    } catch (error) {
      const errorMsg = `Failed to fetch user notification preferences for userId=${userId}, siteId=${siteId}: ${
        error instanceof Error ? error.message : error
      }`;
      error.message = errorMsg;
      throw error;
    }
  }
}
