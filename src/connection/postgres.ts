import pg from 'pg';
import { postgresConfig } from '../config/postgres';

const { Pool } = pg;

class PostgresConnection {
  private pool: pg.Pool;

  constructor() {
    this.pool = new Pool({
      user: postgresConfig.DB_USER,
      host: postgresConfig.DB_HOST,
      database: postgresConfig.DB_NAME,
      password: postgresConfig.DB_PASSWORD,
      port: postgresConfig.DB_PORT,
    });
  }

  async query<T>(sql: string, params?: any[]): Promise<pg.QueryResult<T>> {
    let client: pg.PoolClient | undefined;
    try {
      client = await this.pool.connect();
      return await client.query(sql, params);
    } catch (error) {
      error.message = `Failed to execute query: ${error.message}`;
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  async close(): Promise<void> {
    try {
      if (this.pool) {
        await this.pool.end();
      }
    } catch (error) {
      error.message = `Failed to close PostgreSQL pool: ${error.message}`;
      throw error;
    }
  }
}

export default new PostgresConnection();
