import { Kafka, Consumer, EachMessagePayload, CompressionTypes, CompressionCodecs, SASLOptions } from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import { v4 as uuidv4 } from 'uuid';
import { kafkaConfig } from '../config/kafka';

CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;

class KafkaConsumerConnection {
  private static instance: KafkaConsumerConnection;
  private client: Kafka | null;
  private consumer: Consumer | null;
  private isConnected: boolean;
  private readonly clientIdPrefix = 'alert-notification-service';

  constructor() {
    if (KafkaConsumerConnection.instance) {
      return KafkaConsumerConnection.instance;
    }
    this.client = null;
    this.consumer = null;
    this.isConnected = false;
    KafkaConsumerConnection.instance = this;
  }

  public static getInstance(): KafkaConsumerConnection {
    if (!KafkaConsumerConnection.instance) {
      KafkaConsumerConnection.instance = new KafkaConsumerConnection();
    }
    return KafkaConsumerConnection.instance;
  }

  private async initialize(): Promise<void> {
    try {
      const { KAFKA_SECURITY_PROTOCOL, KAFKA_BROKERS, KAFKA_USERNAME, KAFKA_PASSWORD, CONNECTION_TIMEOUT } =
        kafkaConfig;

      const sasl: SASLOptions | undefined =
        KAFKA_SECURITY_PROTOCOL === 'SASL_SSL'
          ? {
              mechanism: 'scram-sha-512',
              username: KAFKA_USERNAME,
              password: KAFKA_PASSWORD,
            }
          : undefined;

      const config = {
        clientId: `${this.clientIdPrefix}-consumer-${uuidv4()}`,
        brokers: KAFKA_BROKERS,
        connectionTimeout: CONNECTION_TIMEOUT,
        ...(KAFKA_SECURITY_PROTOCOL === 'SASL_SSL' && {
          ssl: true,
          sasl,
        }),
      };

      this.client = new Kafka(config);
      this.consumer = this.client.consumer({ groupId: `${this.clientIdPrefix}-group-${uuidv4()}` });
    } catch (error) {
      error.message = `Failed to initialize Kafka consumer: ${error.message}`;
      throw error;
    }
  }

  async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        return;
      }

      if (!this.consumer) {
        await this.initialize();
      }

      await this.consumer?.connect();
      this.isConnected = true;
    } catch (error) {
      this.isConnected = false;
      error.message = `Failed to connect Kafka consumer: ${error.message}`;
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (!this.isConnected) {
        return;
      }

      await this.consumer?.disconnect();
      this.isConnected = false;
    } catch (error) {
      error.message = `Failed to disconnect Kafka consumer: ${error.message}`;
      throw error;
    }
  }

  async subscribe(topic: string): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      await this.consumer?.subscribe({ topic, fromBeginning: false });
    } catch (error) {
      error.message = `Failed to subscribe to Kafka topic ${topic}: ${error.message}`;
      throw error;
    }
  }

  async consume(messageHandler: (payload: EachMessagePayload) => Promise<void>): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      await this.consumer?.run({
        eachMessage: async (payload: EachMessagePayload) => {
          try {
            await messageHandler(payload);
          } catch (error) {
            error.message = `Failed to process message: ${error.message}`;
            throw error;
          }
        },
      });
    } catch (error) {
      error.message = `Failed to start consuming messages: ${error.message}`;
      throw error;
    }
  }
}

export default KafkaConsumerConnection;
