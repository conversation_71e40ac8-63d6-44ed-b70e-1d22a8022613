import { createClient, RedisClientType } from 'redis';
import { redisConfig } from '../config/redis';
import logger from '../utils/logger';

class RedisConnection {
  private static instance: RedisConnection;
  private client: RedisClientType;

  constructor() {
    if (RedisConnection.instance) {
      return RedisConnection.instance;
    }

    this.client = createClient({
      url: `${redisConfig.REDIS_PROTOCOL}://${redisConfig.REDIS_HOST}:${redisConfig.REDIS_PORT}`,
      password: redisConfig.REDIS_PASSWORD,
    });

    this.client.on('error', (error) => {
      logger.error(`Redis error: ${error.message}`);
    });

    RedisConnection.instance = this;
  }

  public static getInstance(): RedisConnection {
    if (!RedisConnection.instance) {
      RedisConnection.instance = new RedisConnection();
    }
    return RedisConnection.instance;
  }

  async connect(): Promise<void> {
    if (!this.client.isOpen) {
      await this.client.connect();
    }
  }

  async get(key: string): Promise<string | null> {
    await this.connect();
    return this.client.get(key);
  }

  async set(key: string, value: string, ttlSeconds: number): Promise<void> {
    await this.connect();
    await this.client.setEx(key, ttlSeconds, value);
  }

  async mget(keys: string[]): Promise<(string | null)[]> {
    await this.connect();
    return this.client.mGet(keys);
  }
}

export default RedisConnection;
