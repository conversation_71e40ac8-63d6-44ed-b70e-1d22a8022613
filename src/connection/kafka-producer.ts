import { Ka<PERSON><PERSON>, Producer, CompressionTypes, CompressionCodecs, SASLOptions } from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import { v4 as uuidv4 } from 'uuid';
import { kafkaConfig } from '../config';

CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;

class KafkaProducerConnection {
  private static instance: KafkaProducerConnection;
  private client: Kafka | null;
  private producer: Producer | null;
  private isConnected: boolean;
  private readonly clientIdPrefix = 'alert-notification-service';

  constructor() {
    if (KafkaProducerConnection.instance) {
      return KafkaProducerConnection.instance;
    }
    this.client = null;
    this.producer = null;
    this.isConnected = false;
    KafkaProducerConnection.instance = this;
  }

  public static getInstance(): KafkaProducerConnection {
    if (!KafkaProducerConnection.instance) {
      KafkaProducerConnection.instance = new KafkaProducerConnection();
    }
    return KafkaProducerConnection.instance;
  }

  private async initialize(): Promise<void> {
    try {
      const { KAFKA_SECURITY_PROTOCOL, KAFKA_BROKERS, KAFKA_USERNAME, KAFKA_PASSWORD, CONNECTION_TIMEOUT } =
        kafkaConfig;

      const sasl: SASLOptions | undefined =
        KAFKA_SECURITY_PROTOCOL === 'SASL_SSL'
          ? {
              mechanism: 'scram-sha-512',
              username: KAFKA_USERNAME,
              password: KAFKA_PASSWORD,
            }
          : undefined;

      const config = {
        clientId: `${this.clientIdPrefix}-producer-${uuidv4()}`,
        brokers: KAFKA_BROKERS,
        connectionTimeout: CONNECTION_TIMEOUT,
        ...(KAFKA_SECURITY_PROTOCOL === 'SASL_SSL' && {
          ssl: true,
          sasl,
        }),
      };

      this.client = new Kafka(config);
      this.producer = this.client.producer();
    } catch (error) {
      error.message = `Failed to initialize Kafka producer: ${error.message}`;
      throw error;
    }
  }

  async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        return;
      }

      if (!this.producer) {
        await this.initialize();
      }

      await this.producer?.connect();
      this.isConnected = true;
    } catch (error) {
      this.isConnected = false;
      error.message = `Failed to connect Kafka producer: ${error.message}`;
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (!this.isConnected) {
        return;
      }

      await this.producer?.disconnect();
      this.isConnected = false;
    } catch (error) {
      error.message = `Failed to disconnect Kafka producer: ${error.message}`;
      throw error;
    }
  }

  async sendMessage(topic: string, message: unknown): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      await this.producer?.send({
        topic,
        compression: CompressionTypes.Snappy,
        messages: [{ value: JSON.stringify(message) }],
      });
    } catch (error) {
      error.message = `Failed to send message to Kafka topic ${topic}: ${error.message}`;
      throw error;
    }
  }
}

export default KafkaProducerConnection;
