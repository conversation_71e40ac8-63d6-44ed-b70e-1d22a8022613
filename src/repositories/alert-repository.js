import PostgresConnection from '../connections/postgres.js';
import logger from '../utils/logger.js';

class AlertRepository {
  constructor() {
    this.postgres = new PostgresConnection();
  }

  /**
   * Find incidents that need reminder notifications
   * @returns {Promise<Array>} - Pending reminder incidents
   */
  async findPendingReminders() {
    const critical = 15; // 15m
    const high = 60; // 1h
    const medium = 360; // 6h
    const low = 720; // 12h

    const query = `
      SELECT
        aih.id,
        aih.issue_occurred_at,
        aih.alert_inventory_id,
        ai.name as alert_name,
        COALESCE(ai.severity, at.severity) as severity,
        ai.siteid,
        ai.asset_id,
        ai.alert_template_ref_id,
        at.alert_category,
        at.observer_execution_ref_id,
        CASE
          WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'critical' THEN ${critical}
          WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'high' THEN ${high}
          WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'medium' THEN ${medium}
          WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'low' THEN ${low}
          ELSE ${medium}
        END AS interval_minutes
      FROM
        alert_incident_history aih
      INNER JOIN
        alert_inventory ai ON aih.alert_inventory_id = ai.id
      INNER JOIN
        alert_template at ON ai.alert_template_ref_id = at.id
      WHERE
        aih.issue_resolved_at IS NULL
        AND aih.acknowledge_ts IS NULL
        AND ai.status <> 0
        AND at.status <> 0
        AND (
          aih.last_notification_triggerred_at IS NULL
          OR
          aih.last_notification_triggerred_at AT TIME ZONE 'UTC' <=
            (CURRENT_TIMESTAMP AT TIME ZONE 'UTC') -
            (CASE
              WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'critical' THEN ${critical}
              WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'high' THEN ${high}
              WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'medium' THEN ${medium}
              WHEN LOWER(COALESCE(ai.severity, at.severity)) = 'low' THEN ${low}
              ELSE ${medium}
            END) * INTERVAL '1 minute'
        )
      ORDER BY
        aih.issue_occurred_at ASC;
    `;

    try {
      const result = await this.postgres.query(query);
      logger.info(`Found ${result.rows.length} incidents pending reminders`);
      return result.rows;
    } catch (error) {
      logger.error('Failed to fetch pending reminders', { error: error.message });
      throw error;
    }
  }

  /**
   * Update the last notification timestamp for an incident
   * @param {number} incidentId - The incident ID
   * @returns {Promise<Object>} - Updated incident
   */
  async updateLastNotificationTime(incidentId) {
    const query = `
      UPDATE alert_incident_history
      SET
        last_notification_triggerred_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
      WHERE id = $1
      RETURNING id;
    `;

    try {
      const result = await this.postgres.query(query, [incidentId]);

      if (result.rows.length === 0) {
        throw new Error(`No incident found with ID: ${incidentId}`);
      }

      return result.rows[0];
    } catch (error) {
      logger.error(`Failed to update last notification time: ${error.message}`);
      throw error;
    }
  }
}

export default new AlertRepository();
