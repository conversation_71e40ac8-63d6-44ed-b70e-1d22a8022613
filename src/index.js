import 'dotenv/config';
import { runReminderProcess } from './jobs/reminder.job.js';
import PostgresConnection from './connections/postgres.js';
import redisClient from './connections/redis.js';
import kafkaConnection from './connections/kafka.js';
import logger from './utils/logger.js';
import { NODE_ENV } from './config/index.js';

logger.info(`Starting smart-alert-reminder-service in ${NODE_ENV} environment`);

/**
 * Initialize and start the service
 */
async function startService() {
  try {
    // Initialize connections
    const postgres = new PostgresConnection();
    await kafkaConnection.connect();

    // Run the reminder process once
    await runReminderProcess();

    logger.info('🧩 Reminder process completed successfully');

    return { postgres };
  } catch (error) {
    logger.error(`Failed to run service: ${error.message}`, { error });
    throw error;
  } finally {
    // Close connections after process is complete
    await gracefulShutdown('PROCESS_COMPLETE', { postgres: new PostgresConnection() });
  }
}

/**
 * Handle graceful shutdown
 */
async function gracefulShutdown(signal, resources) {
  logger.info(`Received ${signal}, starting graceful shutdown`);

  try {
    // Disconnect from Kafka
    await kafkaConnection.disconnect();
    logger.info('Kafka connection closed');

    // Close PostgreSQL connection
    if (resources?.postgres) {
      await resources.postgres.close();
      logger.info('PostgreSQL connection closed');
    }

    // Close Redis connection
    await redisClient.quit();
    logger.info('Redis connection closed');

    logger.info('Reminder service shutdown completed');

    if (signal !== 'PROCESS_COMPLETE') {
      process.exit(0);
    }
  } catch (error) {
    logger.error(`Error during service shutdown: ${error.message}`, { error });
    process.exit(1);
  }
}

// Run the service
try {
  await startService();
  process.exit(0);
} catch (error) {
  process.exit(1);
}

// Handle signals for graceful shutdown
process.on('SIGTERM', () => gracefulShutdown('SIGTERM', {}));
process.on('SIGINT', () => gracefulShutdown('SIGINT', {}));

// Catch uncaught exceptions and unhandled rejections
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error: error.message, stack: error.stack });
  gracefulShutdown('uncaughtException', {});
});

process.on('unhandledRejection', (reason) => {
  logger.error('Unhandled rejection', {
    error: reason instanceof Error ? reason.message : String(reason),
    stack: reason instanceof Error ? reason.stack : undefined
  });
  gracefulShutdown('unhandledRejection', {});
});
