import Joi from 'joi';

const configSchema = Joi.object()
  .keys({
    REDIS_HOST: Joi.string().required(),
  })
  .unknown(true);

const { error } = configSchema.validate(process.env);
if (error) throw new Error(`Redis config validation error: ${error.message}`);

const redisConfig = {
  REDIS_HOST: process.env.REDIS_HOST || 'localhost',
  REDIS_PORT: Number.parseInt(process.env.REDIS_PORT) || 6379,
  REDIS_PROTOCOL: process.env.REDIS_PROTOCOL || 'redis',
  LOCK_TTL: Number.parseInt(process.env.REDIS_LOCK_TTL) || 3000, // 3 seconds
};

export default redisConfig;
