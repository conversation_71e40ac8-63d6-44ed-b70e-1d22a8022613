export const redisConfig = {
  REDIS_PROTOCOL: process.env.REDIS_PROTOCOL || 'redis',
  REDIS_HOST: process.env.REDIS_HOST || 'localhost',
  REDIS_PORT: parseInt(process.env.REDIS_PORT || '6379'),
  REDIS_DB: parseInt(process.env.REDIS_DB || '0'),
  REDIS_USERNAME: process.env.REDIS_USERNAME,
  REDIS_PASSWORD: process.env.REDIS_PASSWORD,
  PING_INTERVAL: parseInt(process.env.REDIS_PING_INTERVAL || '2000'),
  MAX_RETRY_TIME: parseInt(process.env.REDIS_MAX_RETRY_TIME || '3600000'), // 1 hour
  MAX_RETRY_ATTEMPTS: parseInt(process.env.REDIS_MAX_RETRY_ATTEMPTS || '10'),
};
