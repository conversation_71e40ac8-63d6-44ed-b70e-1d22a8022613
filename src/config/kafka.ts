export const kafkaConfig = {
  KAFKA_SECURITY_PROTOCOL: process.env.KAFKA_SECURITY_PROTOCOL || 'PLAIN',
  KAFKA_BROKERS: (process.env.KAFKA_BROKERS || '').split(','),
  KAFKA_USERNAME: process.env.KAFKA_USERNAME,
  KAFKA_PASSWORD: process.env.KAFKA_PASSWORD,
  CONNECTION_TIMEOUT: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT || '30000'),
  TOPICS: {
    ALERT_NOTIFICATION: process.env.<PERSON>AFKA_TOPIC,
    EMAIL: process.env.KAFKA_TOPIC_EMAIL,
    WHATSAPP: process.env.KAFKA_TOPIC_WHATSAPP,
    SMS: process.env.KAFKA_TOPIC_SMS,
  },
};
