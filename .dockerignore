# Version control
.git
.gitignore
.github

# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment files
.env
.env.*
!.env.example

# Development files
**/*.test.js
**/*.spec.js
coverage
.vscode
.idea
*.md
!README.md

# Logs
logs
*.log

# Build artifacts
dist
build

# Docker
Dockerfile
.dockerignore

# Other
.DS_Store
.editorconfig
.eslintrc
.eslintignore
.prettierrc
.prettierignore
