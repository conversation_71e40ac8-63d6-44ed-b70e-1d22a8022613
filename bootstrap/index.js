import logger from '../utils/logger.js';
import { checkRedisConnectivity } from '../connections/redis.js';
import PostgresConnection from '../connections/postgres.js';
import kafkaConnection from '../connections/kafka.js';
import * as Sentry from '@sentry/node';

export default async function bootstrap() {
  global.logger = logger;
  global.gracefulShutdown = async function () {
    await Sentry.flush();
    process.exit(1);
  };

  // Initialize Kafka producer
  try {
    await kafkaConnection.connect();
    logger.info('Kafka producer initialized and connected');
  } catch (error) {
    logger.error(`Failed to initialize Kafka producer: ${error.message}`, error);
    throw error;
  }

  await checkRedisConnectivity();

  const postgresConnection = new PostgresConnection();
  await postgresConnection.isAuthenticated();

  process.on('uncaughtException', async (error) => {
    logger.error(`uncaughtException: ${error.message}`, error);
    await gracefulShutdown();
  });
  process.on('unhandledRejection', async (error) => {
    logger.error(`unhandledRejection: ${error.message}`, error);
    await gracefulShutdown();
  });
}
