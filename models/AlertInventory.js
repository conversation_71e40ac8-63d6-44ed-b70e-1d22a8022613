// models/AlertInventory.js
import { DataTypes } from 'sequelize';
import sequelize from '../connections/db.js'; // Import the sequelize instance from your db.js

const AlertInventory = sequelize.define(
  'AlertInventory',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    alert_template_ref_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    siteid: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    severity: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    asset_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    asset_type: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    escalation_time_in_min: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    escalated_to: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: true,
      field: 'createdAt',
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: true,
      field: 'updatedAt',
    },
  },
  {
    tableName: 'alert_inventory',
    timestamps: false, // Set to false because you're handling timestamps manually
    underscored: true, // This ensures that the snake_case is used for foreign keys like alert_template_ref_id
  },
);

// Set up association with the alert_template model (assuming it exists)
AlertInventory.associate = (models) => {
  AlertInventory.belongsTo(models.AlertTemplate, {
    foreignKey: 'alert_template_ref_id',
    as: 'alertTemplate', // Alias for this relationship
  });
};

export default AlertInventory;
