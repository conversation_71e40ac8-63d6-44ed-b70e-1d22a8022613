import { DataTypes } from 'sequelize';
import sequelize from '../connections/db.js';

const AlertTemplate = sequelize.define(
  'AlertTemplate',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'createdAt',
    },
    created_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    last_updated_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'updatedAt',
    },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    observer_source: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    observer_execution_ref_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    config: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    expression: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    default_priority: {
      type: DataTypes.SMALLINT,
      allowNull: true,
    },
    template_category: {
      type: DataTypes.SMALLINT,
      defaultValue: 1,
      allowNull: false,
    },
    alert_category: {
      type: DataTypes.STRING(255),
      defaultValue: 'cpa',
      allowNull: false,
    },
    severity: {
      type: DataTypes.STRING(255),
      defaultValue: 'low',
      allowNull: false,
    },
    misc: {
      type: DataTypes.JSONB,
      defaultValue: '[]',
      allowNull: true,
    },
  },
  {
    tableName: 'alert_template',
    timestamps: false, // Disable timestamps because they are being manually managed
    underscored: true, // Automatically converts camelCase to snake_case
    validate: {
      checkAlertCategory() {
        const allowedCategories = ['recipe', 'cpa', 'iot_health'];
        if (!allowedCategories.includes(this.alert_category)) {
          throw new Error('Invalid alert category');
        }
      },
      checkSeverity() {
        const allowedSeverities = ['low', 'medium', 'high', 'critical'];
        if (!allowedSeverities.includes(this.severity)) {
          throw new Error('Invalid severity');
        }
      },
      checkTemplateCategory() {
        const allowedCategories = [1, 2];
        if (!allowedCategories.includes(this.template_category)) {
          throw new Error('Invalid template category');
        }
      },
    },
  },
);

// Association setup with AlertInventory (if needed)
AlertTemplate.associate = (models) => {
  AlertTemplate.hasMany(models.AlertInventory, {
    foreignKey: 'alert_template_ref_id',
    as: 'alertInventories', // Alias for the relationship
  });
};

export default AlertTemplate;
