import pg from 'pg';
import { postgresConfig } from '../config/index.js';
import logger from '../utils/logger.js';

const { Pool } = pg;

class PostgresConnection {
  constructor() {
    if (PostgresConnection.instance) {
      return PostgresConnection.instance;
    }
    this.pool = new Pool({
      user: postgresConfig.DB_USER,
      host: postgresConfig.DB_HOST,
      database: postgresConfig.DB_NAME,
      password: postgresConfig.DB_PASSWORD,
      port: postgresConfig.DB_PORT,
    });

    PostgresConnection.instance = this;
  }

  static async beginTransaction() {
    if (!PostgresConnection.instance) {
      new PostgresConnection();
    }
    const client = await PostgresConnection.instance.pool.connect();
    await client.query('BEGIN');

    return {
      execute: async (sql, params) => await client.query(sql, params),
      commit: async () => {
        try {
          await client.query('COMMIT');
        } finally {
          client.release();
        }
      },
      rollback: async () => {
        try {
          await client.query('ROLLBACK');
        } finally {
          client.release();
        }
      }
    };
  }

  async query(sql, params) {
    const client = await this.pool.connect();
    try {
      return await client.query(sql, params);
    } finally {
      client.release();
    }
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
    }
  }

  async isAuthenticated() {
    let client;
    try {
      client = await this.pool.connect();
      const result = await client.query('SELECT 1');
      return result.rows.length === 1;
    } catch (error) {
      logger.error(`Postgres Connection Error: ${error.message}`, error);
      return false;
    } finally {
      if (client) {
        client.release();
      }
    }
  }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  try {
    if (PostgresConnection.instance) {
      await PostgresConnection.instance.close();
      logger.info('PostgreSQL pool has been closed');
    }
  } catch (error) {
    logger.error(`Error closing PostgreSQL pool: ${error.message}`, error);
  }
});

export default PostgresConnection;
