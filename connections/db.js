import { postgresConfig } from '../config/index.js';
import { Sequelize } from 'sequelize';

const sequelize = new Sequelize(postgresConfig.DB_NAME, postgresConfig.DB_USER, postgresConfig.DB_PASSWORD, {
  host: postgresConfig.DB_HOST,
  dialect: 'postgres',
  logging: false,
  pool: {
    max: 5, // Maximum number of connections in the pool
    min: 0, // Minimum number of connections in the pool
    acquire: 30000, // Maximum time (in ms) to wait for a connection before throwing an error
    idle: 10000, // Maximum time (in ms) a connection can be idle before being released
  },
});
(async () => {
  try {
    await sequelize.authenticate();
    console.log('Connection to the database has been established successfully.');
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  }
})();

export default sequelize;
