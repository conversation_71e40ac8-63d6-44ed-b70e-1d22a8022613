import redis from 'redis';
import logger from '../utils/logger.js';
import { redisConfig } from '../config/index.js';

function getRetryStrategy() {
  return function (options) {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      logger.error('The server refused the connection, will retry...', {
        attempt: options.attempt,
      });
      return Math.min(options.attempt * 100, 3000);
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      logger.error('Retry time exhausted');
      return new Error('Retry time exhausted');
    }
    if (options.attempt > 10) {
      return undefined;
    }
    return Math.min(options.attempt * 100, 3000);
  };
}

const redisClient = redis.createClient({
  url: `${redisConfig.REDIS_PROTOCOL}://${redisConfig.REDIS_HOST}:${redisConfig.REDIS_PORT}`,
  retry_strategy: getRetryStrategy(),
});

redisClient.on('error', (error) => {
  logger.error(`Redis connection error: ${error.message}`, error);
  process.exit(1);
});

redisClient.on('connect', () => {
  logger.info('Redis connected', { host: redisConfig.REDIS_HOST });
});

redisClient.on('ready', () => {
  logger.info('Redis client is ready');
});

redisClient.on('warning', (warning) => {
  logger.error('Warning from Redis client', { warning });
});

redisClient.on('end', () => {
  logger.error('Redis connection ended');
  process.exit(1);
});

let pingIntervalId;
function setupPing() {
  const pingInterval = 2000;
  pingIntervalId = setInterval(async () => {
    try {
      const response = await redisClient.ping();
      logger.info(`Ping response: ${response}`);
    } catch (error) {
      logger.error(`Ping error: ${error.message}`, error);
    }
  }, pingInterval);
}

async function checkRedisConnectivity() {
  try {
    const response = await redisClient.ping();
    logger.info('Redis connectivity check successful', { response });
  } catch (error) {
    logger.error(`Redis connectivity check failed: ${error.message}`, error);
    throw error;
  }
}

process.on('SIGINT', () => {
  clearInterval(pingIntervalId);
  redisClient.quit();
});

(async () => {
  await redisClient.connect();
  setupPing();
})();

export default redisClient;
export { checkRedisConnectivity };
