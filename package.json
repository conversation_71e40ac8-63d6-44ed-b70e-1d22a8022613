{"name": "smart_alert_notification_service", "version": "1.0.0", "description": "Service to handle alert notifications and distribute them to appropriate channels", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node src/index.ts", "build": "tsc"}, "keywords": ["alerts", "notifications", "kafka", "typescript"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@types/node": "^22.10.2", "@types/pg": "^8.11.10", "@types/uuid": "^8.3.4", "@types/redis": "^4.0.11", "eslint": "^9.17.0", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.716.0", "@aws-sdk/lib-dynamodb": "^3.716.0", "@influxdata/influxdb-client": "^1.35.0", "@sentry/node": "^8.51.0", "dotenv": "^16.4.7", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "moment-timezone": "^0.5.46", "pg": "^8.13.1", "redis": "^4.6.13", "snappy": "^7.2.2", "uuid": "^8.3.2", "winston": "^3.17.0", "winston-transport-sentry-node": "^3.0.0", "zod": "^3.24.1"}}