{"name": "alert-brain-microservice", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@influxdata/influxdb-client": "^1.35.0", "@sentry/node": "^8.40.0", "dotenv": "^16.3.1", "joi": "^17.13.3", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "redis": "^4.6.7", "sequelize": "^6.37.5", "uuid": "^9.0.0", "winston": "^3.10.0", "winston-transport-sentry-node": "^3.0.0"}}