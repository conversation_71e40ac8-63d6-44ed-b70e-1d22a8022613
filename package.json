{"name": "smart-alert-reminder-service", "version": "1.0.0", "description": "Service to send reminders for unacknowledged alerts", "type": "module", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "lint": "eslint src/**/*.js", "format": "prettier --write 'src/**/*.js'"}, "keywords": ["alerts", "reminders", "notifications"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"eslint": "^8.56.0", "prettier": "^3.1.1"}, "dependencies": {"@sentry/node": "^7.86.0", "dotenv": "^16.3.1", "redis": "^4.6.11", "joi": "^17.11.0", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "moment-timezone": "^0.5.43", "pg": "^8.11.3", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-transport-sentry-node": "^2.8.0"}}