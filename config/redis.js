import Joi from 'joi';

const configSchema = Joi.object()
  .keys({
    REDIS_HOST: Joi.string().required(),
  })
  .unknown(true);
const { error } = configSchema.validate(process.env);

if (error) {
  throw new Error(`${error.message}`);
}

const redisConfig = {
  REDIS_HOST: process.env.REDIS_HOST,
  REDIS_PORT: Number.parseInt(process.env.REDIS_PORT) || 6379,
  REDIS_PROTOCOL: process.env.REDIS_PROTOCOL || 'redis',
};
export default redisConfig;
