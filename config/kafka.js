import Jo<PERSON> from 'joi';

const configSchema = Joi.object()
  .keys({
    KAFKA_BROKERS: Joi.string().required(),
    TOPIC: Joi.string().required(),
    KAFKA_CONSUMER_GROUP_ID: Joi.string().required(),
    KAFKA_CONSUMER_ID: Joi.string().required(),
    KAFKA_USERNAME: Joi.string().required(),
    KAFKA_PASSWORD: Joi.string().required(),
    KAFKA_SECURITY_PROTOCOL: Joi.string().required(),
    NOTIFICATION_QUEUE_TOPIC: Joi.string(),
    METRICS_TOPIC: Joi.string(),
  })
  .unknown(true);
const { error } = configSchema.validate(process.env);
if (error) throw new Error(`${error.message}`);
const kafkaConfig = {
  KAFKA_BROKERS: process.env.KAFKA_BROKERS.split(','),
  TOPIC: process.env.TOPIC,
  KAFKA_CONSUMER_GROUP_ID: process.env.KAFKA_CONSUMER_GROUP_ID,
  KAFKA_CONSUMER_ID: process.env.KAFKA_CONSUMER_ID,
  KAFKA_USERNAME: process.env.KAFKA_USERNAME,
  KAFKA_PASSWORD: process.env.KAFKA_PASSWORD,
  KAFKA_SECURITY_PROTOCOL: process.env.KAFKA_SECURITY_PROTOCOL,
  MAX_BYTES_PER_PARTITION: Number.parseInt(process.env.MAX_BYTES_PER_PARTITION) || 2097152,
  CONNECTION_TIMEOUT: process.env.KAFKA_CONNECTION_TIMEOUT || 3000,
  NOTIFICATION_QUEUE_TOPIC: process.env.NOTIFICATION_QUEUE_TOPIC || 'SmartAlert.NotificationQueue',
  METRICS_TOPIC: process.env.METRICS_TOPIC || 'SmartAlert.Metrics',
};
export default kafkaConfig;
