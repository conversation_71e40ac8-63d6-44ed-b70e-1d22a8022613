import Joi from 'joi';

const configSchema = Joi.object()
  .keys({
    DB_HOST: Joi.string().required(),
    DB_USER: Joi.string().required(),
    DB_PASSWORD: Joi.string().allow('', null),
    DB_NAME: Joi.string().required(),
  })
  .unknown(true);
const { error } = configSchema.validate(process.env);
if (error) throw new Error(`${error.message}`);

const postgresConfig = {
  DB_HOST: process.env.DB_HOST,
  DB_PORT: Number.parseInt(process.env.DB_PORT) || 5432,
  DB_USER: process.env.DB_USER,
  DB_PASSWORD: process.env.DB_PASSWORD,
  DB_NAME: process.env.DB_NAME,
};
export default postgresConfig;
