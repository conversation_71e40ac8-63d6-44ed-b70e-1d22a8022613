import Joi from 'joi';

export function isValidAlertEvent(messagePacketSchema) {
  const messageSchema = Joi.object()
    .keys({
      alertId: Joi.string().required(),
      siteId: Joi.string().required(),
      eventName: Joi.string().required().valid('OCCURRED', 'RESOLVED'),
      timestamp: Joi.date().iso().required(),
    })
    .unknown(true);

  const { error } = messageSchema.validate(messagePacketSchema);
  if (error) return false;
  return true;
}
