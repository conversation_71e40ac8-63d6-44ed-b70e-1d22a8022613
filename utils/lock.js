import redisClient from '../connections/redis.js';
import logger from './logger.js';

const LOCK_TTL = 30000; // 30s

/**
 * Acquires a distributed lock using Redis
 * @param {string} lockKey - The key to use for the lock
 * @returns {Promise<boolean>}
 */
async function acquireLock(lockKey) {
  try {
    const result = await redisClient.set(lockKey, '1', {
      NX: true,
      PX: LOCK_TTL,
    });

    return result === 'OK';
  } catch (error) {
    logger.error(`Error acquiring lock: ${error.message}`, { lockKey, error });
    return false;
  }
}

/**
 * Releases a distributed lock
 * @param {string} lockKey - The key of the lock to release
 */
async function releaseLock(lockKey) {
  try {
    await redisClient.del(lockKey);
  } catch (error) {
    logger.error(`Error releasing lock: ${error.message}`, { lockKey, error });
  }
}

/**
 * Executes an operation with a distributed lock
 * @param {string} lockKey - The key to use for the lock
 * @param {Function} operation - The async operation to execute while holding the lock
 * @returns {Promise<any>} - The result of the operation, or null if lock couldn't be acquired
 */
export async function withLock(lockK<PERSON>, operation) {
  const acquired = await acquireLock(lockKey);

  if (!acquired) {
    logger.info('Lock already held, skipping operation', { lockKey });
    return null;
  }

  try {
    return await operation();
  } finally {
    await releaseLock(lockKey);
  }
}
