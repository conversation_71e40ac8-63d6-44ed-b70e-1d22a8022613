import kafkaConnection from '../connections/kafka.js';
import logger from './logger.js';
import { kafkaConfig } from '../config/index.js';

const METRICS_TOPIC = kafkaConfig.METRICS_TOPIC;

export async function emitAlertMetric(metricPayload) {
  try {
    await kafkaConnection.sendMessage(METRICS_TOPIC, metricPayload);

    logger.debug('Alert metric emitted successfully', {
      topic: METRICS_TOPIC,
      payload: metricPayload,
    });
  } catch (error) {
    logger.error('Failed to emit alert metric', {
      topic: METRICS_TOPIC,
      payload: metricPayload,
      error: error.message,
    });
  }
}
