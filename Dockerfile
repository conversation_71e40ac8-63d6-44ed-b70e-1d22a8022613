FROM node:18-alpine

WORKDIR /app

# Install production dependencies first (better layer caching)
COPY package*.json ./
RUN npm i --only=production

# Create and set proper permissions for node user
RUN mkdir -p /app/node_modules && chown -R node:node /app

# Copy application code
COPY --chown=node:node . .

# Set environment variables
ENV NODE_ENV=production
ENV NODE_OPTIONS="--experimental-specifier-resolution=node"

# Run as non-root user for security
USER node

# Expose the port if your service listens on one
# EXPOSE 3000

# Health check (uncomment and adjust if your service has a health endpoint)
# HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:3000/health || exit 1

# Start the application
CMD ["node", "src/index.js"]
