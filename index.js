import 'dotenv/config';
import Kafka<PERSON><PERSON> from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import { v4 as uuidv4 } from 'uuid';
import { kafkaConfig } from './config/index.js';
import { AlertBrainFacade } from './services/AlertBrainFacade.js';
import { flushInflux } from './repository/IncidentInflux.repository.js';
import logger from './utils/logger.js';
import kafkaConnection from './connections/kafka.js';
import redisClient from './connections/redis.js';
import PostgresConnection from './connections/postgres.js';

const { Kafka, CompressionTypes, CompressionCodecs } = KafkaJS;

CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;

await import('./bootstrap/index.js').then((bootstrap) => bootstrap.default());

(async function app() {
  let KafkaClient;
  const {
    KAFKA_SECURITY_PROTOCOL,
    KAFKA_BROKERS,
    KAFKA_CONSUMER_GROUP_ID,
    KAFKA_USERNAME,
    KAFKA_PASSWORD,
    KAFKA_CONSUMER_ID,
    TOPIC,
    CONNECTION_TIMEOUT,
  } = kafkaConfig;

  try {
    if (KAFKA_SECURITY_PROTOCOL === 'SASL') {
      KafkaClient = new Kafka({
        clientId: `${KAFKA_CONSUMER_ID}-${uuidv4()}`,
        brokers: KAFKA_BROKERS,
        ssl: true,
        connectionTimeout: CONNECTION_TIMEOUT,
        sasl: {
          mechanism: 'scram-sha-512',
          username: KAFKA_USERNAME,
          password: KAFKA_PASSWORD,
        },
      });
    } else if (KAFKA_SECURITY_PROTOCOL === 'PLAIN') {
      KafkaClient = new Kafka({
        clientId: `${KAFKA_CONSUMER_ID}-${uuidv4()}`,
        brokers: KAFKA_BROKERS,
        connectionTimeout: CONNECTION_TIMEOUT,
      });
    } else {
      throw new Error(`Only 'PLAIN' and 'SASL' security protocol supported by this application currently`);
    }

    const KafkaConsumer = KafkaClient.consumer({
      groupId: KAFKA_CONSUMER_GROUP_ID,
      maxBytesPerPartition: 2097152,
    });

    await KafkaConsumer.connect();
    logger.debug(`Consumer joined the ${KAFKA_CONSUMER_GROUP_ID}`);
    await KafkaConsumer.subscribe({ topic: TOPIC, fromBeginning: false });
    logger.debug(`Consumer has subscribed to Topic-${TOPIC}`);

    await KafkaConsumer.run({
      eachBatchAutoResolve: false,
      eachBatch: async ({ batch, resolveOffset, heartbeat,commitOffsetsIfNecessary }) => {
        const batchTxnId = uuidv4();
        logger.info(`Event polled from kafka`, {
          batchTxnId,
          initialOffset: batch.messages[0].offset,
          endOffset: batch.messages[batch.messages.length - 1].offset,
          total: batch.messages[batch.messages.length - 1].offset - batch.messages[0].offset,
        });
        try {
          const alertIdWiseEventMap = {};
          for (const { value: messageBuffer,offset:eventOffsetId } of batch.messages) {
            const message = messageBuffer && messageBuffer.toString();
            logger.info(`RAW_ALERT_PACKET`, {
              batchTxnId,
              rawAlertPacket:message,
              eventOffsetId
            });
            let _message;
            try {
              _message = JSON.parse(message);
              _message.eventOffsetId=`eventOffsetRecord-${eventOffsetId}`
            } catch (error) {
              logger.debug('E_RAW_ALERT_PACKET', {
                error: error.message,
                rawAlertPacket:message,
                eventOffsetId
              });
              resolveOffset(eventOffsetId);
              continue;
            }
            if (!_message.alertId) {
              logger.warn('E_RAW_ALERT_PACKET', {
                rawAlertPacket:message,
                eventOffsetId:_message.eventOffsetId
              });
              resolveOffset(eventOffsetId);
              continue;
            }
            //TODO: need to remove it when implementing the notification type event
            if (!['OCCURRED', 'RESOLVED'].includes(_message.eventName)) {
              logger.warn('E_RAW_ALERT_PACKET', {
                eventName: _message.eventName,
                rawAlertPacket:message,
                eventOffsetId:_message.eventOffsetId
              });
              resolveOffset(eventOffsetId);
              continue;
            }
            if (!alertIdWiseEventMap[_message.alertId]) {
              alertIdWiseEventMap[_message.alertId] = [];
            }
            alertIdWiseEventMap[_message.alertId].push(_message);
          }
          async function processEvents(alertEvents) {
            for (const event of alertEvents) {
              const alertBrainFacadeInstance = await AlertBrainFacade.build(event);
              if (!alertBrainFacadeInstance) {
                resolveOffset(event.eventOffsetId.split('-')[1]);
                continue;
              }

              if (alertBrainFacadeInstance.isStateFullEvent()) {
                await alertBrainFacadeInstance.processStateFullEvent();
              } else {
                await alertBrainFacadeInstance.processNotificationEvent();
              }
              resolveOffset(event.eventOffsetId.split('-')[1]);
              await heartbeat();
            }
          }
          await Promise.all(Object.values(alertIdWiseEventMap).map(processEvents));
          await commitOffsetsIfNecessary();
          logger.info(`Event processed successfully`, {
            batchTxnId,
            initialOffset: batch.messages[0].offset,
            endOffset: batch.messages[batch.messages.length - 1].offset,
            total: batch.messages[batch.messages.length - 1].offset - batch.messages[0].offset,
          });
        } catch (error) {
          logger.error(`Unable to Process events batch: msg=${error.message}`, {
            error,
            batchTxnId,
            initialOffset: batch.messages[0].offset,
            endOffset: batch.messages[batch.messages.length - 1].offset,
            total: batch.messages[batch.messages.length - 1].offset - batch.messages[0].offset,
          });
          throw error;
        }
      },
    });

    const shutdown = async (signal) => {
      logger.info(`Received ${signal}, starting graceful shutdown`);
      try {
        const alertStateKeys = await redisClient.keys('alert_state:*');
        if (alertStateKeys.length > 0) {
          await redisClient.del(alertStateKeys);
          logger.info('Successfully cleared alert state cache', {
            clearedKeys: alertStateKeys.length,
          });
        }

        await KafkaConsumer.disconnect();
        logger.info('Successfully disconnected from Kafka consumer');

        await kafkaConnection.disconnect();
        logger.info('Successfully disconnected from Kafka producer');

        await flushInflux();
        logger.info('Successfully flushed InfluxDB connection');

        if (PostgresConnection.instance) {
          await PostgresConnection.instance.close();
          logger.info('Successfully closed PostgreSQL connection');
        }

        await redisClient.quit();
        logger.info('Successfully disconnected from Redis');

        process.exit(0);
      } catch (error) {
        logger.error(`Error during shutdown: ${error.message}`, error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  } catch (error) {
    logger.error(`Fatal error in Kafka consumer: ${error.message}`, error);
    process.exit(1);
  }
})();
